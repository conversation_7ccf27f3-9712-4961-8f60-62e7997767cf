{{-- Mobile Student Form Partial - Loaded via AJAX --}}
<form class="mobile-student-form" data-student-id="{{ $student->id }}">
    @csrf
    
    {{-- Student Progress Overview --}}
    <div class="progress-overview" style="background: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 12px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <strong style="color: #1d1d1f; font-size: 14px;">Current Progress</strong>
            @if($student->last_nouranya)
                <span style="font-size: 12px; color: #34C759; background: rgba(52, 199, 89, 0.1); padding: 2px 8px; border-radius: 12px;">
                    <i class="fa fa-check-circle"></i> Has Reports
                </span>
            @else
                <span style="font-size: 12px; color: #8e8e93; background: rgba(142, 142, 147, 0.1); padding: 2px 8px; border-radius: 12px;">
                    <i class="fa fa-info-circle"></i> New Student
                </span>
            @endif
        </div>
        
        @if($student->nouranya_plans && $student->nouranya_plans->count() > 0)
            @php $plan = $student->nouranya_plans->first(); @endphp
            <div style="font-size: 12px; color: #6d6d72;">
                <div><strong>Current Plan:</strong> 
                    @if($plan->from_lesson && $plan->to_lesson)
                        Lesson {{ $plan->from_lesson }} → {{ $plan->to_lesson }}
                    @elseif($plan->talaqqi_from_lesson && $plan->talaqqi_to_lesson)
                        Talaqqi: {{ $plan->talaqqi_from_lesson }} → {{ $plan->talaqqi_to_lesson }}
                    @elseif($plan->talqeen_from_lesson && $plan->talqeen_to_lesson)
                        Talqeen: {{ $plan->talqeen_from_lesson }} → {{ $plan->talqeen_to_lesson }}
                    @else
                        Plan in progress
                    @endif
                </div>
            </div>
        @endif
    </div>

    {{-- Main Form Fields --}}
    <div class="form-sections">
        
        {{-- Attendance Section --}}
        <div class="form-section" style="margin-bottom: 15px;">
            <h5 style="margin: 0 0 8px 0; color: #1d1d1f; font-size: 14px; font-weight: 600;">
                <i class="fa fa-user-check" style="color: #007AFF;"></i> Attendance
            </h5>
            <div class="form-row">
                <div class="form-group">
                    <label for="attendance_{{ $student->id }}">Status</label>
                    <select name="attendance" id="attendance_{{ $student->id }}" class="form-control" required>
                        <option value="">Select...</option>
                        @foreach($attendanceOptions as $option)
                            <option value="{{ $option->id }}" 
                                {{ $student->nouranya && $student->nouranya->first() && $student->nouranya->first()->attendance_id == $option->id ? 'selected' : '' }}>
                                {{ $option->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        {{-- Memorization Section --}}
        <div class="form-section" style="margin-bottom: 15px; padding: 12px; background: rgba(0, 122, 255, 0.02); border: 1px solid rgba(0, 122, 255, 0.1); border-radius: 8px;">
            <h5 style="margin: 0 0 10px 0; color: #007AFF; font-size: 14px; font-weight: 600;">
                <i class="fa fa-book"></i> Memorization (Hefz)
            </h5>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="from_lesson_{{ $student->id }}">From Lesson</label>
                    <input type="number" name="from_lesson" id="from_lesson_{{ $student->id }}" 
                           class="form-control" min="1" 
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->from_lesson : '' }}"
                           placeholder="e.g. 1">
                </div>
                <div class="form-group">
                    <label for="to_lesson_{{ $student->id }}">To Lesson</label>
                    <input type="number" name="to_lesson" id="to_lesson_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->to_lesson : '' }}"
                           placeholder="e.g. 3">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="from_line_{{ $student->id }}">From Line</label>
                    <input type="number" name="from_line" id="from_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->from_line : '' }}"
                           placeholder="Line #">
                </div>
                <div class="form-group">
                    <label for="to_line_{{ $student->id }}">To Line</label>
                    <input type="number" name="to_line" id="to_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->to_line : '' }}"
                           placeholder="Line #">
                </div>
            </div>
        </div>

        {{-- Talaqqi Section --}}
        <div class="form-section" style="margin-bottom: 15px; padding: 12px; background: rgba(52, 199, 89, 0.02); border: 1px solid rgba(52, 199, 89, 0.1); border-radius: 8px;">
            <h5 style="margin: 0 0 10px 0; color: #34C759; font-size: 14px; font-weight: 600;">
                <i class="fa fa-microphone"></i> Talaqqi (Recitation)
            </h5>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="talaqqi_from_lesson_{{ $student->id }}">From Lesson</label>
                    <input type="number" name="talaqqi_from_lesson" id="talaqqi_from_lesson_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talaqqi_from_lesson : '' }}"
                           placeholder="e.g. 1">
                </div>
                <div class="form-group">
                    <label for="talaqqi_to_lesson_{{ $student->id }}">To Lesson</label>
                    <input type="number" name="talaqqi_to_lesson" id="talaqqi_to_lesson_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talaqqi_to_lesson : '' }}"
                           placeholder="e.g. 3">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="talaqqi_from_line_{{ $student->id }}">From Line</label>
                    <input type="number" name="talaqqi_from_line" id="talaqqi_from_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talaqqi_from_line : '' }}"
                           placeholder="Line #">
                </div>
                <div class="form-group">
                    <label for="talaqqi_to_line_{{ $student->id }}">To Line</label>
                    <input type="number" name="talaqqi_to_line" id="talaqqi_to_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talaqqi_to_line : '' }}"
                           placeholder="Line #">
                </div>
            </div>
        </div>

        {{-- Talqeen Section --}}
        <div class="form-section" style="margin-bottom: 15px; padding: 12px; background: rgba(255, 149, 0, 0.02); border: 1px solid rgba(255, 149, 0, 0.1); border-radius: 8px;">
            <h5 style="margin: 0 0 10px 0; color: #FF9500; font-size: 14px; font-weight: 600;">
                <i class="fa fa-volume-up"></i> Talqeen (Teaching)
            </h5>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="talqeen_from_lesson_{{ $student->id }}">From Lesson</label>
                    <input type="number" name="talqeen_from_lesson" id="talqeen_from_lesson_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talqeen_from_lesson : '' }}"
                           placeholder="e.g. 1">
                </div>
                <div class="form-group">
                    <label for="talqeen_to_lesson_{{ $student->id }}">To Lesson</label>
                    <input type="number" name="talqeen_to_lesson" id="talqeen_to_lesson_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talqeen_to_lesson : '' }}"
                           placeholder="e.g. 3">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="talqeen_from_line_{{ $student->id }}">From Line</label>
                    <input type="number" name="talqeen_from_line" id="talqeen_from_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talqeen_from_line : '' }}"
                           placeholder="Line #">
                </div>
                <div class="form-group">
                    <label for="talqeen_to_line_{{ $student->id }}">To Line</label>
                    <input type="number" name="talqeen_to_line" id="talqeen_to_line_{{ $student->id }}" 
                           class="form-control" min="1"
                           value="{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->talqeen_to_line : '' }}"
                           placeholder="Line #">
                </div>
            </div>
        </div>

        {{-- Evaluation Section --}}
        <div class="form-section" style="margin-bottom: 15px;">
            <h5 style="margin: 0 0 8px 0; color: #1d1d1f; font-size: 14px; font-weight: 600;">
                <i class="fa fa-star" style="color: #FF9500;"></i> Evaluation
            </h5>
            <div class="form-row">
                <div class="form-group">
                    <label for="evaluation_{{ $student->id }}">Performance</label>
                    <select name="nouranya_evaluation_id" id="evaluation_{{ $student->id }}" class="form-control">
                        <option value="">Select evaluation...</option>
                        @foreach($evaluationOptions as $option)
                            <option value="{{ $option->id }}" 
                                {{ $student->nouranya && $student->nouranya->first() && $student->nouranya->first()->nouranya_evaluation_id == $option->id ? 'selected' : '' }}>
                                {{ $option->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        {{-- Notes Section --}}
        <div class="form-section" style="margin-bottom: 15px;">
            <h5 style="margin: 0 0 8px 0; color: #1d1d1f; font-size: 14px; font-weight: 600;">
                <i class="fa fa-sticky-note" style="color: #8e8e93;"></i> Notes
            </h5>
            <div class="form-row">
                <div class="form-group">
                    <label for="notes_{{ $student->id }}">Teacher's Notes</label>
                    <textarea name="nouranya_evaluation_note" id="notes_{{ $student->id }}" 
                              class="form-control" rows="3" 
                              placeholder="Add any notes about student's performance or behavior..."
                              style="resize: vertical; min-height: 60px;">{{ $student->nouranya && $student->nouranya->first() ? $student->nouranya->first()->nouranya_evaluation_note : '' }}</textarea>
                </div>
            </div>
        </div>
    </div>

    {{-- Quick Save Button --}}
    <button type="button" class="quick-save-btn" onclick="quickSaveStudent({{ $student->id }})">
        <i class="fa fa-save"></i> Quick Save
    </button>
    
    {{-- Success/Error Message Container --}}
    <div id="message-{{ $student->id }}" style="margin-top: 10px;"></div>
</form>

{{-- Auto-save functionality --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('[data-student-id="{{ $student->id }}"]');
    let autoSaveTimer;
    
    // Auto-save functionality (debounced)
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(() => {
                autoSaveForm({{ $student->id }});
            }, 2000); // Auto-save after 2 seconds of inactivity
        });
    });
    
    // Validation helpers
    const attendanceSelect = form.querySelector('[name="attendance"]');
    if (attendanceSelect) {
        attendanceSelect.addEventListener('change', function() {
            validateAttendance({{ $student->id }});
        });
    }
});

// Auto-save form (background save)
async function autoSaveForm(studentId) {
    const messageContainer = document.getElementById(`message-${studentId}`);
    
    try {
        const formData = new FormData();
        const container = document.getElementById(`form-container-${studentId}`);
        const inputs = container.querySelectorAll('input, select, textarea');
        
        const reportData = {};
        inputs.forEach(input => {
            if (input.name && input.value) {
                reportData[input.name] = input.value;
            }
        });
        
        formData.append('student_id', studentId);
        formData.append('report_data', JSON.stringify(reportData));
        formData.append('class_time', currentDate);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        
        const response = await fetch(`{{ url('workplace/education/class') }}/${classId}/report/nouranya/quick-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Show subtle auto-save indicator
            messageContainer.innerHTML = `
                <div style="font-size: 11px; color: #34C759; opacity: 0.8; text-align: center;">
                    <i class="fa fa-check"></i> Auto-saved
                </div>
            `;
            setTimeout(() => {
                messageContainer.innerHTML = '';
            }, 2000);
        }
    } catch (error) {
        console.log('Auto-save failed silently:', error);
    }
}

// Validate attendance selection
function validateAttendance(studentId) {
    const attendanceSelect = document.querySelector(`#attendance_${studentId}`);
    const messageContainer = document.getElementById(`message-${studentId}`);
    
    if (attendanceSelect.value === '') {
        messageContainer.innerHTML = `
            <div class="alert alert-warning" style="padding: 6px 10px; font-size: 12px;">
                <i class="fa fa-exclamation-triangle"></i> Please select attendance status
            </div>
        `;
    } else {
        messageContainer.innerHTML = '';
    }
}
</script>
