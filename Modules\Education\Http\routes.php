<?php




//Mail::raw('This is a test email', function ($message) {
//    $message->to('<EMAIL>')->subject('Test Email');
//});
//
//dd(333);
//Schema::create('failed_notifications', function (\Illuminate\Database\Schema\Blueprint $table) {
//    $table->id();
//    $table->unsignedBigInteger('notifiable_id');
//    $table->string('notifiable_type');
//    $table->string('notification_type');
//    $table->text('exception');
//    $table->timestamps();
//});

//use Illuminate\Support\Facades\Mail;

use App\Jobs\SendEmailJob;
use App\MoshafSurah;
use App\Talaqqi;
use App\Talqeen;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Password;
use Modules\Education\Http\Controllers\SponsorAttendanceController;
use Modules\Education\Http\Controllers\ExternalCollaboratorController;
use Modules\Education\Http\Controllers\IjazasanadMemorizationMonthlyPlanController;
use Modules\Education\Http\Controllers\CommentIjazasanadMemorizationMonthlyPlanController;
use Modules\Education\Http\Controllers\MonthlyIjazasanadReportController;
use Modules\Education\Http\Controllers\MonthEndIjazasanadSummaryController;
use Modules\Education\Http\Controllers\MonthEndIjazasanadStudentSummaryController;
use Modules\Education\Http\Controllers\StudentIjazasanadReportsDatatablesController;
use Modules\Education\Http\Controllers\MonthlyNouranyaReportController;
use Modules\Education\Http\Controllers\MonthEndNouranyaSummaryController;
use Modules\Education\Http\Controllers\IjazasanadMonthYearController;
use Modules\Education\Http\Controllers\ClassIjazasanadReportController;






Route::group(['middleware' => ['web', 'auth:employee','missedClockOutMiddleware'], 'prefix' => 'workplace/education', 'namespace' => 'Modules\Education\Http\Controllers'], function () {
    

    Route::get('class-calendar-data/{classId}', \Modules\Education\Http\Controllers\ClassCalendarDataController::class)->name('class-calendar-data');
    Route::get('class-calendar-data/{classId}/students/{date}', [\Modules\Education\Http\Controllers\ClassCalendarDataController::class, 'getStudentDetails'])->name('class-calendar-student-details');
    Route::get('/get-months-with-records/{classId}/{year}', [\Modules\Education\Http\Controllers\MonthlyPlanController::class, 'getMonthsWithRecords'])
        ->name('get-months-with-records');
    
    // Add new route for class navigation dropdown
    Route::get('/classes/grouped-by-program/{currentClassId?}', [\Modules\Education\Http\Controllers\ClassesController::class, 'getClassesGroupedByProgram'])
        ->name('classes.grouped-by-program');
    
    // Routes for different class report types
    Route::get('/classes/{classId}/nouranya-report', function($classId) {
        return view('modules.education.classes.reports.class.nouranya', compact('classId'));
    })->name('education-nouranya-class-report');
    
    Route::get('/classes/{classId}/ijazasanad-report', function($classId) {
        // Redirect to the existing Ijazasanad class report route
        return redirect()->route('month.end.ijazasanad.level1.student.summary.report', ['id' => $classId]);
    })->name('education-ijazasanad-class-report');
    
    Route::get('/classes/{classId}/hefz-report', function($classId) {
        // Redirect to the existing halaqah report route (which handles hefz reports)
        return redirect()->route('class.halaqah.report', ['classId' => $classId]);
    })->name('education-hefz-class-report');
    
    Route::middleware(['role_or_permission:supervisor_2_|education-manager_2_|managing-director_2_|manage student levels'])->group(function () {
        Route::get('/classes/{id}/student-level', [\Modules\Education\Http\Controllers\StudentLevelController::class, 'fetchStudentLevels'])->name('classes.student-level');
        Route::post('/students/{id}/update-level', [\Modules\Education\Http\Controllers\StudentLevelController::class, 'updateStudentLevel'])->name('students.update-level')->middleware('permission:manage student levels');
    });
    Route::post('/get-class-teachers', [\Modules\Education\Http\Controllers\ClassesController::class, 'getClassTeachers'])->middleware('permission:access classes')
        ->name('get.class.teachers');

// Phase 2 disclaimers routes
    Route::get('/disclaimers/accept', [\Modules\Education\Http\Controllers\DisclaimersController::class, 'showAcceptForm'])->name('disclaimers.accept');
    Route::post('/disclaimers/accept', [\Modules\Education\Http\Controllers\DisclaimersController::class, 'accept'])->name('disclaimers.accept.submit');
    Route::middleware(['auth', 'center.limited'])->group(function () {
        Route::get('/attendance/sponsor-report', [SponsorAttendanceController::class, 'monthlyReport'])
            ->name('attendance.sponsor.report');
    });





    // List all external collaborators
    Route::get('/', [ExternalCollaboratorController::class, 'index'])
        ->name('external.collab.index');

    // Show form to create new external collaborator
    Route::get('/create', [ExternalCollaboratorController::class, 'create'])
        ->name('external.collab.create');

    // Store new external collaborator
    Route::post('/store', [ExternalCollaboratorController::class, 'store'])
        ->name('external.collab.store')
        ->middleware('permission:add student');

    // Show form to edit an external collaborator (centers assigned, etc.)
    Route::get('/{user}/edit', [ExternalCollaboratorController::class, 'edit'])
        ->name('external.collab.edit');

    // Update the collaborator
    Route::post('/{user}/update', [ExternalCollaboratorController::class, 'update'])
        ->name('external.collab.update')
        ->middleware('permission:update student');

    // Enable or disable
    Route::post('/{user}/toggle-status', [ExternalCollaboratorController::class, 'toggleStatus'])
        ->name('external.collab.toggleStatus')
        ->middleware('permission:update student');


// routes/web.php
    Route::get('/hr/external-users/logs', function () {
        $logs = \App\AuditLog::orderByDesc('id')->paginate(20);
        return view('humanresource.external_collab.logs', compact('logs'));
    })->middleware(['auth'])->name('external.collab.logs');

    Route::get('/students-with-missing-data-count', 'StudentMissingDataController@missingDataCount')->name('students.missing-data-count');;
    Route::get('/students-require-email-verification-count', 'StudentRequireEmailVerificationController@requireEmailVerificationCount')->name('students.require-email-verification');;
    Route::post('program-levels/{programId}/save-property', 'ProgramLevelPropertiesController@store')->name('saveProperty')->middleware('permission:update class program');
    Route::post('program-levels/{programlevelId}/update-property', 'ProgramLevelPropertiesController@update')->name('updateLessonProperty')->middleware('permission:update class program');
// Define the POST route for updating Talaqqi values
    Route::post('/updateTalaqqi', [\Modules\Education\Http\Controllers\TableController::class, 'updateTalaqqi'])->middleware('permission:update class report');
    Route::post('/deleteTalaqqi', [\Modules\Education\Http\Controllers\TableController::class, 'deleteTalaqqi'])->middleware('permission:remove class report');
    Route::post('/deleteTalqeen', [\Modules\Education\Http\Controllers\TableController::class, 'deleteTalqeen'])->middleware('permission:remove class report');

// Define the POST route for updating Talqeen values
    Route::post('/updateTalqeen', [\Modules\Education\Http\Controllers\TableController::class, 'updateTalqeen'])->middleware('permission:update class report');
    Route::post('/update-student-level', 'ClassesController@updateStudentLevel')->name('update.student.level')->middleware('permission:manage student levels');
    Route::post('/update-nouranyana-program-student-level', 'ClassesController@updateNouranyaProgramStudentLevel')->name('update.nouranya.program.student.level')->middleware('permission:manage student levels');

    Route::get('/program-levels/{programLevelId}/sequential-lessons/{fromLessonId}', 'ProgramLevelsController@getSequentialLessons')
        ->name('program-levels.get-sequential-lessons')
        ->middleware('auth');
    Route::get('/student/{studentId}/nouranya-plans/{yearMonth}/current-to-lesson', 'StudentNouranyaPlansController@getCurrentToLesson')
        ->name('student-nouranya-plans.current-to-lesson')
        ->middleware('auth');
    Route::get('/program-levels/{programLevelId}/lessons', 'ProgramLevelsController@getLessonsByProgramLevel')
        ->name('program-levels.get-lessons');
    Route::post('/student/{studentId}/update-nouranya-to-lesson', 'StudentNouranyaPlansController@updateToLesson')->name('student-nouranya-plans.update-to-lesson')->middleware('permission:update monthly plan');
    Route::post('/student/{studentId}/update-ijazasanad-level1-to-lesson', [IjazasanadMemorizationMonthlyPlanController::class, 'updateToLesson'])->name('student-ijazasanad-level1-plans.update-to-lesson')->middleware('permission:update monthly plan');

    Route::post('/student/{studentId}/update-ijazasanad-level1-talqeen-to-lesson', 'IjazaSanadLevel1TalqeenController@updateToLesson')->name('student-ijazasanad-level1-plans.update-talqeen-to-lesson');
    Route::post('/student/{studentId}/update-ijazasanad-level1-revision-to-lesson', 'IjazaSanadLevel1RevisionController@updateToLesson')->name('student-ijazasanad-level1-plans.update-revision-to-lesson');
    Route::post('/student/{studentId}/update-ijazasanad-level1-jazariyah-to-lesson', 'IjazaSanadLevel1JazariyahController@updateToLesson')->name('student-ijazasanad-level1-plans.update-jazariyah-to-lesson');
    Route::post('/student/{studentId}/update-ijazasanad-level1-seminars-to-lesson', 'IjazaSanadLevel1SeminarsController@updateToLesson')->name('student-ijazasanad-level1-plans.update-seminars-to-lesson');

    Route::post('/student/{studentId}/update-nouranya-to-lesson-talaqqi', 'StudentNouranyaPlansController@updateTalaqqiToLesson')->name('student-nouranya-plans.update-to-lesson-talaqqi')->middleware('auth');
    Route::post('/student/{studentId}/update-nouranya-to-lesson-talqeen', 'StudentNouranyaPlansController@updateTalqeenToLesson')->name('student-nouranya-plans.update-to-lesson-talqeen')->middleware('auth');
    Route::post('/student/{studentId}/update-nouranya-line-number', 'StudentNouranyaPlansController@updateLineNumber')
        ->name('student-nouranya-plans.update-line-number')
        ->middleware('permission:update monthly plan');
    Route::post('/student/{studentId}/update-nouranya-report-line-number', 'StudentNouranyaDailyReportsController@updateLineNumber')
        ->name('student-nouranya-report.update-line-number')
        ->middleware('permission:update class report');
    Route::post('/student/{studentId}/update-nouranya-to-lesson-line-number', 'StudentNouranyaPlansController@updateToLessonLineNumber')->name('student-nouranya-plans.update-to-lesson-line-number')->middleware('permission:update monthly plan');
    Route::post('/student/{studentId}/update-ijazasanad-level1-to-lesson-line-number', [IjazasanadMemorizationMonthlyPlanController::class, 'updateToLessonLineNumber'])->name('student-ijazasanad-level1-plans.update-to-lesson-line-number')
        ->middleware('permission:update monthly plan');
    Route::post('/student/{studentId}/update-nouranya-report-to-lesson-line-number', 'StudentNouranyaDailyReportsController@updateToLessonLineNumber')
        ->name('student-nouranya-report.update-to-lesson-line-number')
        ->middleware('permission:update class report');

// routes/web.php
    Route::get('/program-level-options/{programLevelId}', 'ProgramLevelOptionsController@getProgramLevelOptions');

    Route::get('/program-levels/{programLevelId}/related-lessons', 'ProgramLevelsController@fetchRelatedLessonsAndUpdatePlan')->name('program-levels.related-lessons');
    Route::get('/program-levels/{programLevelId}/ijazasanad-related-lessons', 'ProgramLevelsController@fetchRelatedIjazasanadLessonsAndUpdatePlan')->name('program-levels.ijazasanad-related-lessons');
    Route::post('/student/{studentId}/update-program-level', 'ProgramLevelsController@updateStudentProgramLevel')->name('student.update-program-level')->middleware('permission:manage student levels');
    Route::post('/student/{studentId}/update-ijazasanad-program-level', 'ProgramLevelsController@updateStudentIjazasanadProgramLevel')->name('student.update-ijazasanad-program-level')->middleware('permission:manage student levels');
    Route::get('/student/{studentId}/get-to-lessons', 'LessonController@getToLessons')->name('student.get-to-lessons');
    Route::get('/student/{studentId}/daily-nouranya-report/get-to-lessons', 'LessonController@dailyNouranyaReportgetToLessons')->name('daily.nouranya.report.student.get-to-lessons');

    Route::get('/student/{studentId}/daily-ijazasanad-report/get-talqeen-to-lessons', 'LessonController@dailyIjazasanadReportgetTalqeenToLessons')->name('daily.ijazasanad.report.student.get-talqeen-to-lessons');
    Route::get('/student/{studentId}/daily-ijazasanad-report/get-level1-revision-to-lessons', 'LessonController@dailyIjazasanadReportgetLevel1RevisionToLessons')->name('daily.ijazasanad.report.student.get-level1-revision-to-lessons');
    Route::get('/student/{studentId}/daily-ijazasanad-report/get-jazariyah-to-lessons', 'LessonController@dailyIjazasanadReportgetJazariyahToLessons')->name('daily.ijazasanad.report.student.get-jazariyah-to-lessons');
    Route::get('/student/{studentId}/daily-ijazasanad-report/get-seminars-to-lessons', 'LessonController@dailyIjazasanadReportgetSeminarsToLessons')->name('daily.ijazasanad.report.student.get-seminars-to-lessons');

    Route::post('/student/{studentId}/update-program-level-talaqqi', 'ProgramLevelsController@updateStudentTalaqqiProgramLevel')->name('student.update-program-level-talaqqi');

    Route::post('/student/{studentId}/update-ijazasanad-program-level-talqeen', 'ProgramLevelsController@updateStudentIjazasanadTalqeenProgramLevel')->name('student.update-ijazasanad-program-level-talqeen');
    Route::post('/student/{studentId}/update-ijazasanad-program-level-revision', 'ProgramLevelsController@updateStudentIjazasanadRevisionProgramLevel')->name('student.update-ijazasanad-program-level-revision');
    Route::post('/student/{studentId}/update-ijazasanad-program-level-jazariyah', 'ProgramLevelsController@updateStudentIjazasanadJazariyahProgramLevel')->name('student.update-ijazasanad-program-level-jazariyah');
    Route::post('/student/{studentId}/update-ijazasanad-program-level-seminars', 'ProgramLevelsController@updateStudentIjazasanadSeminarsProgramLevel')->name('student.update-ijazasanad-program-level-seminars');

    Route::get('/student/{studentId}/get-talaqqi-to-lessons', 'ProgramLevelsController@getTalaqqitoLessons')->name('student.get-talaqqi-to-lessons');
    Route::post('/student/{studentId}/update-program-level-talqeen', 'ProgramLevelsController@updateStudentTalqeenProgramLevel')->name('student.update-program-level-talqeen');
    Route::get('/student/{studentId}/get-lesson-lines', 'ProgramLevelsController@getLessonLines')->name('get.lesson.lines');
    Route::get('/student/{studentId}/nouranya-report/get-lesson-lines', 'ClassNouranyaReportController@getLessonLines')->name('nouranya.report.get.lesson.lines');
    Route::get('/student/{studentId}/get-to-lesson-lines', 'ProgramLevelsController@getToLessonLines')->name('get.to.lesson.lines');
    Route::get('/student/{studentId}/nouranya-report-get-to-lesson-lines', 'ProgramLevelsController@getNouranyaReportToLessonLines')->name('nouranya.report.get.to.lesson.lines');

    Route::get('/revision-remarks/{reportId}', function ($reportId) {

        $report = \App\StudentRevisionReport::find($reportId);

        if ($report) {
            return response()->json(['revision_evaluation_note' => $report->revision_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });

    Route::get('/revision-remarks/ijazasanad/{reportId}', function ($reportId) {

        $report = \App\StudentIjazasanadRevisionReport::find($reportId);

        if ($report) {
            return response()->json(['revision_evaluation_note' => $report->revision_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });
    Route::get('/memorization-remarks/{reportId}', function ($reportId) {

        $report = \App\StudentHefzReport::find($reportId);

        if ($report) {
            return response()->json(['hefz_evaluation_note' => $report->hefz_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });
    Route::get('/memorization-remarks/ijazasanad/{reportId}', function ($reportId) {

        $report = \App\StudentIjazasanadMemorizationReport::find($reportId);

        if ($report) {
            return response()->json(['ijazasanad_evaluation_note' => $report->ijazasanad_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });

    Route::get('/nouranya-remarks/{reportId}', function ($reportId) {

        $report = \App\StudentNouranyaReport::find($reportId);

        if ($report) {
            return response()->json(['nouranya_evaluation_note' => $report->nouranya_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });

    // Enhanced remarks system routes
    Route::get('/nouranya-remarks-enhanced/{reportId}', 'ClassNouranyaReportController@getEnhancedRemarks');
    Route::get('/nouranya-remarks-templates', 'ClassNouranyaReportController@getRemarksTemplates');
    Route::post('/memorization-remarks', function () {


        $reportId = request()->input('report_id');
        $content = request()->input('content');

        $report = \App\StudentHefzReport::find($reportId);
        if ($report) {
            $report->hefz_evaluation_note = $content;
            $report->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }

    })->middleware('permission:update class report');
    Route::post('/memorization-remarks/ijazasand', function () {


        $reportId = request()->input('report_id');
        $content = request()->input('content');

        $report = \App\StudentIjazasanadMemorizationReport::find($reportId);
        if ($report) {
            $report->ijazasanad_evaluation_note = $content;
            $report->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }

    })->middleware('permission:update class report');

    Route::post('/nouranya-remarks', function () {


        $reportId = request()->input('report_id');
        $content = request()->input('content');

        $report = \App\StudentNouranyaReport::find($reportId);
        if ($report) {
            $report->nouranya_evaluation_note = $content;
            $report->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }

    })->middleware('permission:update class report');
    Route::post('/revision-remarks', function () {


        $reportId = request()->input('report_id');
        $content = request()->input('content');

        $report = \App\StudentRevisionReport::find($reportId);
        if ($report) {
            $report->revision_evaluation_note = $content;
            $report->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }

    })->middleware('permission:update class report');
    Route::post('/revision-remarks/ijazasanad/', function () {


        $reportId = request()->input('report_id');
        $content = request()->input('content');

        $report = \App\StudentIjazasanadRevisionReport::find($reportId);
        if ($report) {
            $report->revision_evaluation_note = $content;
            $report->save();
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 404);
        }

    })->middleware('permission:update class report');


//
//$to_name = 'RECEIVER_NAME';
//$to_email = '<EMAIL>';
//$data = array('name' => "Ogbonna Vitalis(sender_name)", 'body' => "A test mail");
//$order = 123;
//Mail::to($to_email)->send(new \App\Mail\OrderShipped($order));



    Route::delete('/evaluation-schema-option/delete-record/{id}', 'EvaluationSchemaOptionController@deleteRecord')
        ->name('evaluation-schema-option.delete')
        ->middleware('permission:remove evaluation_schema');



//    Route::get('exams-list', 'ExamController@index')->name('exams-list')->middleware('userRolePermission:49');
    Route::get('exams-list', 'ExamController@index')->name('exams-list');
//    Route::get('exams-list/data', 'ExamController@data')->name('exams.data')->middleware('userRolePermission:49');
    Route::get('exams-list/data', 'ExamController@data')->name('exams.data');



    Route::get('student-tables-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTablesPDFController@downloadPDF')->name('education-student-tables-pdf');
    Route::get('class-tables-pdf/class/{classId?}/date/{monthYear?}', 'ClassTablesPDFController@downloadPDF')->name('education-class-tables-pdf');
    Route::get('nouranya-class-tables-pdf/class/{classId?}/date/{monthYear?}', 'NouranyaClassTablesPDFController@downloadPDF')->name('education-nouranya-class-tables-pdf');
    Route::get('ijazasanad/class-tables-pdf/class/{classId?}/date/{monthYear?}', 'IjazasanadClassTablesPDFController@downloadPDF')->name('ijazasanad-education-class-tables-pdf');
    Route::get('ijazasanad/student-tables-pdf/student/{studentId?}/date/{monthYear?}/level/{levelOne?}', 'IjazasanadStudentTablesPDFController@downloadPDF')->name('ijazasanad-education-student-tables-pdf')->defaults('levelOne', 0);;
    Route::get('ijazasanad/student-tables-excel/student/{studentId?}/date/{monthYear?}/level/{levelOne?}', 'IjazasanadStudentTablesPDFController@downloadExcel')->name('ijazasanad-education-student-tables-excel')->defaults('levelOne', 0);
    Route::get('ijazasanad/class-tables-excel/class/{classId?}/date/{monthYear?}', 'IjazasanadStudentTablesPDFController@downloadClassExcel')->name('ijazasanad-education-class-tables-excel');
    Route::get('ijazasanad/level1/class-tables-pdf/class/{classId?}/date/{monthYear?}', 'IjazasanadClassTablesPDFController@downloadPDF')->name('ijazasanad-level1-education-class-tables-pdf');
    Route::get('student-table/memorization-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableMemorizationPDFController@downloadPDF')->name('education.student-memorization-table-pdf');
    Route::get('student-table/revision-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableRevisionPDFController@downloadPDF')->name('education.student-revision-table-pdf');
    Route::get('student-table/month-end-summary-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableMonthEndPDFController@downloadPDF')->name('education.student-month-end-summary-table-pdf');
    Route::get('student-table/nouranya-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableNouranyaPDFController@downloadPDF')->name('education.student-nouranya-table-pdf');
    Route::get('student-table/nouranya-summary-pdf/student/{studentId?}/class/{classId?}/date/{monthYear?}', 'StudentTableNouranyaSummaryPDFController@downloadPDF')->name('education.student-nouranya-summary-table-pdf');

    Route::get('update/program/{id}/from-surat/{suratId}/level/{levelId}', ['as' => 'update.program.from.surat', 'uses' => 'UpdateProgramFromSuratController']);
    Route::get('update/program/{id}/to-surat/{suratId}/level/{levelId}', ['as' => 'update.program.to.surat', 'uses' => 'UpdateProgramToSuratController']);
    Route::get('update/program/{id}/from-lesson/{lessonId}/level/{levelId}', ['as' => 'update.program.from.lesson', 'uses' => 'UpdateProgramFromLessonController']); // dedicated for nurania program
    Route::get('update/program/{id}/to-lesson/{lessonId}/level/{levelId}', ['as' => 'update.program.to.lesson', 'uses' => 'UpdateProgramToLessonController']); // dedicated for nurania program

    // Online Exam
    // Route::resource('online-exam', 'OnlineExamController')->middleware('userRolePermission:238');
    Route::get('online-exam', 'OnlineExamController@index')->name('online-exam')->middleware('userRolePermission:238');
//    Route::get('online-exam/class/{classId}/student/{studentId}', 'AccessStudentOnlineExamsPerClassController')->name('online-exam.class.student');
    Route::get('online-exam/student/{studentId}/class/{classId}', 'AccessStudentOnlineExamsController')->name('online-exam.student');
    Route::get('online-exam/class/{classId}', 'AccessClassOnlineExamsController')->name('online-exam.class');
//    Route::post('online-exam', 'OnlineExamController@store')->name('online-exam')->middleware('userRolePermission:239');
//    Route::post('online-exam', 'OnlineExamController@store')->name('online-exam');
//    Route::get('online-exam/{id}', 'OnlineExamController@edit')->name('online-exam-edit')->middleware('userRolePermission:240');
    Route::get('online-exam/{id}/class/{classId}', 'OnlineExamController@edit')->name('online-exam-edit');
    Route::get('view-online-exam-question/{id}', 'OnlineExamController@viewOnlineExam')->name('online-exam-question-view')->middleware('userRolePermission:238');
    Route::put('online-exam/{id}', 'OnlineExamController@update')->name('online-exam-update')->middleware('permission:update exams list');
    // Route::delete('online-exam/{id}', 'OnlineExamController@delete')->name('online-exam-delete')->middleware('userRolePermission:241');

    Route::post('online-exam-delete', 'OnlineExamController@delete')->name('online-exam-delete')->middleware('permission:remove exams list');
    Route::get('manage-online-exam-question/{id}', ['as' => 'manage_online_exam_question', 'uses' => 'OnlineExamController@manageOnlineExamQuestion'])->middleware('userRolePermission:242');
    Route::post('online_exam_question_store', ['as' => 'online_exam_question_store', 'uses' => 'OnlineExamController@manageOnlineExamQuestionStore'])->middleware('permission:add exams list');

    Route::get('online-exam-publish/{id}', ['as' => 'online_exam_publish', 'uses' => 'OnlineExamController@onlineExamPublish']);
    Route::get('online-exam-publish-cancel/{id}', ['as' => 'online_exam_publish_cancel', 'uses' => 'OnlineExamController@onlineExamPublishCancel']);

    Route::get('online-question-edit/{id}/{type}/{examId}', 'OnlineExamController@onlineQuestionEdit');
    Route::post('online-exam-question-edit', ['as' => 'online_exam_question_edit', 'uses' => 'OnlineExamController@onlineExamQuestionEdit']);
    Route::post('online-exam-question-delete', 'OnlineExamController@onlineExamQuestionDelete')->name('online-exam-question-delete');

    // store online exam question
    Route::post('online-exam-question-assign', ['as' => 'online_exam_question_assign', 'uses' => 'OnlineExamController@onlineExamQuestionAssign']);

    Route::get('view_online_question_modal/{id}', ['as' => 'view_online_question_modal', 'uses' => 'OnlineExamController@viewOnlineQuestionModal']);


    // Online exam marks
    Route::get('online-exam-marks-register/{id}', ['as' => 'online_exam_marks_register', 'uses' => 'OnlineExamController@onlineExamMarksRegister'])->middleware('userRolePermission:243');

    // Route::post('online-exam-marks-store', ['as' => 'online_exam_marks_store', 'uses' => 'OnlineExamController@onlineExamMarksStore']);
    Route::get('online-exam-result/{id}', ['as' => 'online_exam_result', 'uses' => 'OnlineExamController@onlineExamResult'])->middleware('userRolePermission:244');

    Route::get('online-exam-marking/{exam_id}/{s_id}', ['as' => 'online_exam_marking', 'uses' => 'OnlineExamController@onlineExamMarking']);
    Route::post('online-exam-marks-store', ['as' => 'online_exam_marks_store', 'uses' => 'OnlineExamController@onlineExamMarkingStore']);


    Route::resource('program-minimum-score', 'ProgramMinimumScoreToPassController');



    Route::post('program-marks-grade', 'MarksGradeController@store')->name('marks-grade-post');
//    Route::get('program-marks-grade/{id}/{program}', 'MarksGradeController@show')->name('marks-grade-edit')->middleware('userRolePermission:227');
    Route::get('program-marks-grade/{id}/{program}', 'MarksGradeController@show')->name('marks-grade-edit');
//    Route::put('program-marks-grade/{id}/{program}', 'MarksGradeController@update')->name('marks-grade-update')->middleware('userRolePermission:227');
    Route::put('program-marks-grade/{id}/{program}', 'MarksGradeController@update')->name('marks-grade-update');
//    Route::delete('program-marks-grade/{id}/{program}', 'MarksGradeController@destroy')->name('marks-grade-delete')->middleware('userRolePermission:228');
    Route::delete('program-marks-grade/{id}/{program}', 'MarksGradeController@destroy')->name('marks-grade-delete');

    Route::resource('education-calendar', 'AcademicCalendarController');
    Route::resource('education-calendar-ajax', 'AcademicCalendarAjaxController');
//    Route::get('class-routine-report', ['as' => 'class_routine_report', 'uses' => 'class-routine-report@classRoutineReport']);
    Route::post('add-new-class-routine-store', 'ClassRoutineNewController@addNewClassRoutineStore');
    Route::get('get-class-teacher-ajax', 'ClassRoutineNewController@getClassTeacherAjax');
    Route::get('add-new-class-routine-store', 'ClassRoutineNewController@classRoutineSearch');
    Route::get('edit-class-routine/{class_time_id}/{day}/{class_id}/{section_id}/{subject_id}/{room_id}/{assigned_id}/{employee_id}', 'ClassRoutineNewController@addNewClassRoutineEdit');

    Route::get('delete-class-routine-modal/{id}', 'ClassRoutineNewController@deleteClassRoutineModal');
    Route::get('delete-class-routine/{id}', 'ClassRoutineNewController@deleteClassRoutine');
    Route::get('class-routine-new/{class_id}/{section_id}', 'ClassRoutineNewController@classRoutineRedirect');

    Route::post('class-routine-report', 'ClassRoutineNewController@classRoutineReportSearch')->name('class-routine-report');
    Route::post('images/upload', 'ImageController@upload')->name('ckeditor.upload');
    Route::get('class-routine-report', ['as' => 'class_routine_report', 'uses' => 'ClassRoutineNewController@classRoutineReport']);
    Route::get('teacher-class-routine-report', ['as' => 'teacher_class_routine_report', 'uses' => 'ClassRoutineNewController@teacherClassRoutineReport']);
    Route::post('teacher-class-routine-report', 'ClassRoutineNewController@teacherClassRoutineReportSearch')->name('teacher-class-routine-report');
    Route::get('/', 'EducationController@index');

//    Route::resource('dailyreport', 'NewDailyClassReportController');

    Route::get('dailyreport', 'NewDailyClassReportController@index')->name('dailyreport')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:access dailyreport');
    Route::post('dailyreport', 'NewDailyClassReportController@store')->name('dailyreport.store')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:add role');
    Route::get('dailyreport/create', 'NewDailyClassReportController@create')->name('dailyreport.create')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:show role create form');
    Route::get('dailyreport/{id}', 'NewDailyClassReportController@show')->name('dailyreport.show')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:show role');
    Route::get('dailyreport/{id}/edit', 'NewDailyClassReportController@show')->name('dailyreport.edit')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:show role edit form');
    Route::put('dailyreport/{id}', 'NewDailyClassReportController@update')->name('dailyreport.update')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:update role');
    Route::delete('dailyreport/{id}', 'NewDailyClassReportController@destroy')->name('dailyreport.destroy')->middleware('role:administrative_2_|managing-director_2_|human-resource_2_|it-officer_2_', 'permission:remove role');


    Route::post('dailyreport-editor', function (\App\DataTables\DailyClassReportsDataTableEditor $editor) {
        return $editor->process(request());
    });
    Route::post('program-offer-letter', ['as' => 'program_offer_letter', 'uses' => 'ProgramsController@programOfferLetter']);
    Route::get('program-offer-letter/{id}', ['as' => 'get_program_offer_letter', 'uses' => 'ProgramsController@getProgramOfferLetter']);

    Route::get('class/{classId}/report/student/', 'StudentReportsController')->name('class.student.report');
    Route::get('class/student-memorization-report', 'StudentReportsDatatablesController@studentRecords')->name('class.student-memorization-report');
    Route::get('class/student-nouranya-report', 'StudentNouranyaReportsDatatablesController@studentRecords')->name('class.student-nouranya-report');
    Route::get('class/ijazasanad/student-memorization-report', 'StudentIjazasanadReportsDatatablesController@studentRecords')->name('class.ijazasanad.student-memorization-report');

    // level 1 student reports tables data
    Route::get('class/ijazasanad/level1/student-talqeen-report', 'StudentIjazasanadLevel1TalqeenReportsDatatablesController@studentRecords')->name('class.ijazasanad.level1.student-talqeen-report');
    Route::get('class/ijazasanad/level1/student-revision-report', 'StudentIjazasanadLevel1RevisionReportsDatatablesController@studentRecords')->name('class.ijazasanad.level1.student-revision-report');
    Route::get('class/ijazasanad/level1/student-jazariyah-report', 'StudentIjazasanadLevel1JazariyahReportsDatatablesController@studentRecords')->name('class.ijazasanad.level1.student-jazariyah-report');
    Route::get('class/ijazasanad/level1/student-seminar-report', 'StudentIjazasanadLevel1SeminarReportsDatatablesController@studentRecords')->name('class.ijazasanad.level1.student-seminar-report');
    Route::get('classes/{id}/ijazasanad/level1/month-end-student-summary-report', 'MonthEndIjazasanadLevel1StudentSummaryController')->name('month.end.ijazasanad.level1.student.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('classes/{id}/ijazasanad/level1/month-end-student-summary-report2', 'MonthEndIjazasanadLevel1StudentSummaryController2')->name('month.end.ijazasanad.level1.student.summary.report2')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');

    Route::get('class/{classId}/report/halaqah', 'HalaqahReportsController')->name('class.halaqah.report');
    Route::get('class/{classId}/report/nouranya', 'ClassNouranyaReportController@index')->name('class.nouranya.report');

    // Mobile Nouraniyah Report Routes for Enhanced Performance
    Route::get('class/{classId}/report/nouranya/mobile', 'ClassNouranyaReportController@mobileCreate')->name('class.nouranya.mobile.report')->middleware('permission:access class');
    Route::get('class/{classId}/student/{studentId}/form', 'ClassNouranyaReportController@ajaxLoadStudentForm')->name('class.nouranya.mobile.student.form')->middleware('permission:access class');
    Route::post('class/{classId}/report/nouranya/quick-save', 'ClassNouranyaReportController@quickSaveReport')->name('class.nouranya.mobile.quick.save')->middleware('permission:add class nouranya report');

    // Nouranya Report Routes
    Route::get('class/{classId}/halaqah-month-years/', [\Modules\Education\Http\Controllers\HalaqahMonthYearController::class, 'getMonthYears'])->name('class.halaqah.month-years');
    Route::get('class/{classId}/ijazasanad/halaqah-month-years/', [\Modules\Education\Http\Controllers\IjazasanadHalaqahMonthYearController::class, 'getMonthYears'])->name('class.ijazasanad.halaqah.month-years');
    Route::get('class/student-month-years/{student}/{class}', [\Modules\Education\Http\Controllers\MonthYearController::class, 'getMonthYears']);
    Route::get('class/student-nouranya-month-years/{studentId}/{classId}', [\Modules\Education\Http\Controllers\NouranyaMonthYearController::class, 'getMonthYears']);
    Route::get('class/ijazasanad/student-month-years/{student}/{class}', [\Modules\Education\Http\Controllers\IjazasanadMonthYearController::class, 'getMonthYears']);
    Route::get('class/student-ijazasanad-month-years/{studentId}/{classId}', [\Modules\Education\Http\Controllers\IjazasanadMonthYearController::class, 'getMonthYears'])->name('student.ijazasanad.month-years');

    Route::resource('centers', 'CentersController')->middleware('permission:access centers');;
//    Route::resource('classes', 'ClassesController');

    Route::put('update/student/exam/readiness', 'ClassesController@updateStudentExamReadiness')->name('classes.updateStudentExamReadiness')->middleware('permission:access classes');
    Route::get('classes', 'ClassesController@index')->name('classes.index')->middleware('permission:access classes');
    Route::post('classes', 'ClassesController@store')->name('classes.store')->middleware('permission:add class');
    Route::get('classes/create', 'ClassesController@create')->name('classes.create')->middleware('permission:show class create form');
    Route::get('classes/{id}', 'ClassesController@show')->name('classes.show')->middleware('permission:show class');
    Route::get('classes/{id}/edit', 'ClassesController@edit')->name('classes.edit')->middleware('permission:show class edit form');
    Route::match(['put', 'patch'], 'classes/{id}', 'ClassesController@update')->name('classes.update')->middleware('permission:update class');
    Route::delete('classes/{id}', 'ClassesController@destroy')->name('classes.destroy')->middleware('permission:remove class');

    Route::resource('programs', 'ProgramsController')->middleware('permission:access programs');
    Route::resource('program-levels', 'ProgramLevelsController');

    Route::get('reports', 'ReportsController@show');
    Route::get('centers-reports', 'CentersReportsController')->name('centers.reports')->middleware('role_or_permission:supervisor_2_|access centers reports');

    Route::get('student-attendance-reports', 'StudentsAttendanceReportsController')->name('student.attendance.reports');
    Route::post('reports/classes', 'ReportsController@classes')->name('education::reports.classes');
    Route::post('reports/classes/get', 'ReportsController@generate')->name('education::reports.generate');



    Route::get('/get-highlighted-examed-days', function(){
        //get data from database
        $days = \App\OnlineExam::all();

        //return data in json format
        return response()->json($days);
    })->name('get.highlighted.examed.days');

    Route::post('program-centers', 'ProgramsController@centers');

    Route::post('class-programs', 'ClassesController@updateClassPrograms')->name('class.update_programs');
    Route::post('/class/update-class-code', [\Modules\Education\Http\Controllers\ClassesController::class, 'updateClassCode'])->name('class.update_class_code');

//    Route::get('classes/{id}/reports', 'ClassReportController@index')->name('class.reports');


//    Route::get('classes/{class_id}/reports/{report_id}/prepare', 'ClassReportController@prepareReport')->name('class.reports.prepare');
    Route::get('classes/{class}/reports/{report}/prepare', 'NewDailyClassReportController@prepareReport')->name('class.reports.prepare');
    Route::post('classes/{class_id}/reports/{report_id}/prepare', 'ClassReportController@storeTempReport')->name('class.reports.storeTemp');
    Route::post('classes/{class_id}/reports/{report_id}/submit', 'ClassReportController@storeFinalReport')->name('class.reports.submit');

    Route::get('public-holidays', '\Modules\HumanResource\Http\Controllers\PublicHolidaysCalendar');
    Route::get('classes/{id}/reports/create', 'ClassReportController@create')
        ->name('reports.create')
        ->middleware('permission:show report create form');
    // class reports routes
//    Route::resource('classes/{id}/reports', 'ClassReportController'); //->name('class.reports.create');
//    Route::get('classes/{id}/reports', 'ClassReportController@index')->name('reports.index')->middleware('permission:access classes');
    Route::get('classes/{id}/reports', 'ClassReportController@index')->name('reports.index')->middleware('permission:access class');
    Route::get('classes/{id}/calendar', 'ClassReportController@calendar')->name('reports.calendar')->middleware('permission:access class');
    Route::post('classes/{id}/reports', 'ClassReportController@store')->name('reports.store')->middleware('permission:add class report');
    Route::post('classes/{id}/ijazasanad-reports', 'IjazasanadClassReportController@store')->name('ijazasanad.reports.store')->middleware('role_or_permission:supervisor_2_|add class report');
    Route::post('classes/{id}/nouranya-reports', 'ClassNouranyaReportController@store')->name('nouranya.reports.store')->middleware('role_or_permission:supervisor_2_|add class nouranya report');

    Route::get('/classes/calendar/events', 'ClassesDataForCalendarController@getClasses')->name('calendar.events');
    Route::get('/get-classes-data-for-monthly-halaqah-report-table', function () {
        $classes = \App\Classes::all();

            return response()->json($classes);
            });

    Route::get('classes/{id}/reports/{reportId}', 'ClassReportController@show')->name('reports.show')->middleware('permission:view class report');
    Route::get('classes/{id}/reports/{reportId}/edit', 'ClassReportController@edit')->name('reports.edit')->middleware('permission:show class edit form');
    Route::put('classes/{id}/reports/{reportId}', 'ClassReportController@update')->name('reports.update')->middleware('permission:update class');
    Route::delete('classes/{id}/reports/{reportId}', 'ClassReportController@destroy')->name('reports.destroy')->middleware('permission:remove class');
    Route::post('classes/{id}/revision/reports', 'ClassRevisionController@store')->name('revisions.store')->middleware('permission:add revision record');
    Route::post('classes/{id}/revision/ijazasanad-reports', 'IjazasandClassRevisionController@store')->name('ijazasanad.revisions.store')->middleware('role_or_permission:supervisor_2_|add revision record');

    // monthly Plan routes
    Route::get('monthly-plan', 'MonthlyPlanController@index')->name('monthly-plan')->middleware('permission:access monthly plan');
    Route::put('add-comment-hefz-plan', 'CommentHefzMonthlyPlanController@update')->name('add-comment-hefz-plan')->middleware('permission:comment on monthly hefz plan');
    Route::put('add-comment-ijazasanad-memorization-plan', [CommentIjazasanadMemorizationMonthlyPlanController::class, 'update'])->name('add-comment-ijazasanad-memorization-plan')->middleware('permission:comment on monthly hefz plan');
    Route::put('add-comment-ijazasanad-revision-plan', 'CommentIjazasanadRevisionMonthlyPlanController@update')->name('add-comment-ijazasanad-revision-plan')->middleware('permission:comment on monthly revision plan');
    Route::put('add-comment-revision-plan', 'CommentRevisionMonthlyPlanController@update')->name('add-comment-revision-plan')->middleware('permission:comment on monthly revision plan');
    Route::put('add-comment-nouranya-plan', 'CommentNouranyaMonthlyPlanController@update')->name('add-comment-nouranya-plan')->middleware('permission:comment on monthly nouranya plan');
    Route::put('add-comment-ijazasanadLevel1-plan', 'CommentIjazasanadLevel1MonthlyPlanController@update')->name('add-comment-ijazasanadLevel1-plan')->middleware('permission:comment on monthly ijazasanadLevel1 plan');
    Route::post('monthly-plan', 'MonthlyPlanController@store')->name('monthly-plan.store')->middleware('permission:add monthly plan');
    Route::post('ijazasanad-monthly-plan', [IjazasanadMemorizationMonthlyPlanController::class, 'store'])->name('ijazasanad-monthly-plan.store')->middleware('permission:add monthly plan');
    Route::get('monthly-plan/create', 'MonthlyPlanController@create')->name('monthly-plan.create')->middleware('permission:show monthly plan create form');
    Route::get('monthly-plan/{id}/{from_date}', 'MonthlyPlanController@show')->name('monthly-plan.show')->middleware('permission:show monthly plan');
    Route::get('monthly-plan/{id}/edit', 'MonthlyPlanController@edit')->name('monthly-plan.edit')->middleware('permission:show monthly plan edit form');
    Route::put('monthly-plan/{id}', 'MonthlyPlanController@update')->name('monthly-plan.update')->middleware('permission:update monthly plan');
    Route::delete('monthly-plan/{id}', 'MonthlyPlanController@destroy')->name('monthly-plan.destroy')->middleware('permission:remove monthly plan');
    Route::get('hefz-monthly-plan/{hefzPlanId}/to-surah/{surah}/get-ayats', function (Request $request, $hefzPlanId, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition

        if (isset($surah)) {
            $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
            $num_ayat = $surah->num_ayat;

            for ($i = 1; $i <= $num_ayat; $i++) {


                if ($hefzPlan->start_from_surat === $surah->id) {


                    if ($i > $hefzPlan->start_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }

                } else {

                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }


            }
        }

        return $options;
    });


    // load monthly plan comments if already wrote
    Route::get('hefz-monthly-plan/{id}/get-comment', 'CommentHefzMonthlyPlanController@show')->name('hefz-monthly-plan.comment.show')->middleware('permission:show hefz monthly plan comment');


    Route::get('revision-monthly-plan/{id}/get-comment', 'CommentRevisionMonthlyPlanController@show')->name('revision-monthly-plan.comment.show')->middleware('permission:show revision monthly plan comment');


    // Revision Monthly Plan
    Route::post('revision-monthly-plan', 'RevisionMonthlyPlanController@store')->name('revision-monthly-plan.store')->middleware('permission:add monthly plan');
    Route::post('ijazasanad-revision-monthly-plan', '\Modules\Education\Http\Controllers\IjazasanadRevisionMonthlyPlanController@store')->name('ijaza-sanad-revision-monthly-plan.store')->middleware('permission:add monthly plan');

    Route::get('hefz-plan/surah/{surah}/get-ayats', function (Request $request, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition


        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';

            }
        }

        return $options;
    });

    // student hefz achievement report routes



    // student hefz achievement report routes
    Route::get('student-achievement-report', 'StudentMonthlyAchievementReportController@index')->name('student-achievement-report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');
    Route::post('student-achievement-report', 'StudentMonthlyAchievementReportController@store')->name('student-achievement-report.store')->middleware('permission:', 'permission:add achievement report');
    Route::get('student-achievement-report/create', 'StudentMonthlyAchievementReportController@create')->name('student-achievement-report.create')->middleware('permission:', 'permission:show achievement report create form');
    Route::get('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@show')->name('student-achievement-report.show')->middleware('permission:', 'permission:show achievement report');
    Route::get('student-achievement-report/{id}/edit', 'StudentMonthlyAchievementReportController@show')->name('student-achievement-report.edit')->middleware('permission:', 'permission:show achievement report edit form');
    Route::put('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@update')->name('student-achievement-report.update')->middleware('permission:', 'permission:update achievement report');
    Route::delete('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@destroy')->name('student-achievement-report.destroy')->middleware('permission:', 'permission:remove achievement report');


    // student revision achievement report routes
    Route::get('student-revision-achievement-report', 'StudentMonthlyRevisionAchievementReportController@index')->name('student-revision-achievement-report')->middleware('permission:access revision achievement report');
    Route::post('student-revision-achievement-report', 'StudentMonthlyRevisionAchievementReportController@store')->name('student-revision-achievement-report.store')->middleware('permission:add revision achievement report');
    Route::get('student-revision-achievement-report/create', 'StudentMonthlyRevisionAchievementReportController@create')->name('student-revision-achievement-report.create')->middleware('permission:show revision achievement report create form');
    Route::get('student-revision-achievement-report/{id}', 'StudentMonthlyRevisionAchievementReportController@show')->name('student-revision-achievement-report.show')->middleware('permission:show revision achievement report');
    Route::get('student-revision-achievement-report/{id}/edit', 'StudentMonthlyRevisionAchievementReportController@show')->name('student-revision-achievement-report.edit')->middleware('permission:show revision achievement report edit form');
    Route::put('student-revision-achievement-report/{id}', 'StudentMonthlyRevisionAchievementReportController@update')->name('student-revision-achievement-report.update')->middleware('permission:update revision achievement report');
    Route::delete('student-revision-achievement-report/{id}', 'StudentMonthlyRevisionAchievementReportController@destroy')->name('student-revision-achievement-report.destroy')->middleware('permission:remove revision achievement report');


    // student ijazasanad achievement report routes
    Route::get('student-ijazasanad-achievement-report', 'StudentIjazasanadMonthlyAchievementReportController@index')->name('student-ijazasanad-achievement-report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access ijazasanad achievement report');
//    Route::post('student-achievement-report', 'StudentMonthlyAchievementReportController@store')->name('student-achievement-report.store')->middleware('permission:', 'permission:add achievement report');
//    Route::get('student-achievement-report/create', 'StudentMonthlyAchievementReportController@create')->name('student-achievement-report.create')->middleware('permission:', 'permission:show achievement report create form');
//    Route::get('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@show')->name('student-achievement-report.show')->middleware('permission:', 'permission:show achievement report');
//    Route::get('student-achievement-report/{id}/edit', 'StudentMonthlyAchievementReportController@show')->name('student-achievement-report.edit')->middleware('permission:', 'permission:show achievement report edit form');
//    Route::put('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@update')->name('student-achievement-report.update')->middleware('permission:', 'permission:update achievement report');
//    Route::delete('student-achievement-report/{id}', 'StudentMonthlyAchievementReportController@destroy')->name('student-achievement-report.destroy')->middleware('permission:', 'permission:remove achievement report');
//



    Route::resource('org-report', 'OrganizationReportController');

    Route::get('classes/{id}/student-achievement-report', 'ClassReportController@studentRecords');
    Route::get('classes/{id}/student-revision-achievement-report', 'ClassRevisionReportController@studentRecords');
//    Route::get('classes/{id}/month-end-student-summary', 'MonthEndStudentSummaryDatatablesController');
    Route::get('classes/{id}/student-achievement-report-statistics', 'ClassReportController@studentRecordsStatistics');
    Route::get('classes/{id}/org-report-statistics', 'OrganizationReportStatisticsController');
    Route::get('classes/{id}/month-end-student-summary-report', 'MonthEndStudentSummaryController')->name('month.end.student.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('classes/{id}/ijazasanad/month-end-student-summary-report', 'MonthEndIjazasanadStudentSummaryController')->name('month.end.ijazasanad.student.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('student-revision-report', 'StudentRevisionReportsDatatablesController@studentRecords')->name('student-revision-report');
    Route::get('ijazasanad/student-revision-report', 'StudentIjazasanadRevisionReportsDatatablesController@studentRecords')->name('ijazasanad.student-revision-report');
    Route::get('class-wise-student-revision-report', 'ClassWiseStudentRevisionReportsDatatablesController@studentRecords')->name('class-wise-student-revision-report');
    Route::get('ijazasanad/class-wise-student-revision-report', 'IjazasanadClassWiseStudentRevisionReportsDatatablesController@studentRecords')->name('ijazasanad-class-wise-student-revision-report');

//    Route::get('classes/{id}/month-end-halaqah-summary-report', 'MonthEndHalaqahSummaryController')->name('month.end.halaqah.summary.report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');
    Route::get('monthly-halaqah-report', 'MonthlyHalaqahReportController')->name('monthly-halaqah-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/monthly-halaqah-report', 'MonthlyIjazasanadHalaqahReportController')->name('ijazasanad.monthly-halaqah-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');

//   ijazasanad level 1 datatables routes
    Route::get('ijazasanad/level1/monthly-talqeen-report', 'ClassIjazasanadLevel1TalqeenReportsDatatablesController@getRecords')->name('ijazasanad.level1.monthly-talqeen-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/level1/monthly-revision-report', 'ClassIjazasanadLevel1RevisionReportsDatatablesController@getRecords')->name('ijazasanad.level1.monthly-revision-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/level1/monthly-jazariyah-report', 'ClassIjazasanadLevel1JazariyahReportsDatatablesController@getRecords')->name('ijazasanad.level1.monthly-jazariyah-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/level1/monthly-seminar-report', 'ClassIjazasanadLevel1SeminarReportsDatatablesController@getRecords')->name('ijazasanad.level1.monthly-seminar-report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/level1/month-end-summary-report', 'MonthEndIjazasanadLevel1ClassSummaryController@getRecords')->name('ijazasanad.level1.month.end.halaqah.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');


    Route::get('month-end-halaqah-summary-report', 'MonthEndHalaqahSummaryController')->name('month.end.halaqah.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');
    Route::get('ijazasanad/month-end-halaqah-summary-report', 'IjazasanadMonthEndHalaqahSummaryController')->name('ijazasanad.month.end.halaqah.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');

    Route::get('monthly-center-report', 'MonthlyCenterReportController')->name('monthly-center-report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');
    Route::get('month-end-center-summary-report', 'MonthEndCenterSummaryController')->name('month.end.center.summary.report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');

    Route::get('monthly-itqan-report', 'MonthlyItqanReportController')->name('monthly-itqan-report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');
    Route::get('month-end-itqan-summary-report', 'MonthEndItqanSummaryController')->name('month.end.itqan.summary.report')->middleware('role_or_permission:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_', 'permission:access achievement report');

//    Route::resource('students/class/{id}/levels/piechart', 'ReportsTabStudentLevelController');
//    Route::resource('students/class/{id}/attendance/piechart', 'ReportsTabStudentAttendanceController');

    Route::resource('students/class/{id}/levels/piechart', 'ReportsTabStudentLevelController')
        ->names([
            'index' => 'levels_piechart.index',
            'create' => 'levels_piechart.create',
            'store' => 'levels_piechart.store',
            'show' => 'levels_piechart.show',
            'edit' => 'levels_piechart.edit',
            'update' => 'levels_piechart.update',
            'destroy' => 'levels_piechart.destroy',
        ]);

    Route::resource('students/class/{id}/attendance/piechart', 'ReportsTabStudentAttendanceController')
        ->names([
            'index' => 'attendance_piechart.index',
            'create' => 'attendance_piechart.create',
            'store' => 'attendance_piechart.store',
            'show' => 'attendance_piechart.show',
            'edit' => 'attendance_piechart.edit',
            'update' => 'attendance_piechart.update',
            'destroy' => 'attendance_piechart.destroy',
        ]);



    Route::get('students-hefz-levels/piechart', 'StudentsHefzLevelsPieChartController')->name('students.hefz.level.piechart');

//    Route::resource('students/class/{id}/centers/report', 'ReportsTabCentersController');
//    Route::resource('students/class/{id}/centers/halaqah/report', 'ReportsTabHalaqahController');


    Route::resource('students/class/{id}/centers/report', 'ReportsTabCentersController')
        ->names([
            'index' => 'centers_report.index',
            'create' => 'centers_report.create',
            'store' => 'centers_report.store',
            'show' => 'centers_report.show',
            'edit' => 'centers_report.edit',
            'update' => 'centers_report.update',
            'destroy' => 'centers_report.destroy',
        ]);

    Route::resource('students/class/{id}/centers/halaqah/report', 'ReportsTabHalaqahController')
        ->names([
            'index' => 'halaqah_report.index',
            'create' => 'halaqah_report.create',
            'store' => 'halaqah_report.store',
            'show' => 'halaqah_report.show',
            'edit' => 'halaqah_report.edit',
            'update' => 'halaqah_report.update',
            'destroy' => 'halaqah_report.destroy',
        ]);



    Route::post('classes/reports/create', 'ClassReportController@store')->name('class.reports.store');

    Route::post('classes/reports/edit', 'ClassReportController@update')->name('class.reports.update');

    Route::post('classes/add-teacher-to-class', 'ClassesController@addTeacher')->name('class.add_teacher');
    Route::post('classes/add-teachers-to-class', 'ClassesController@addTeachers')->name('class.add_teachers');
    Route::post('/class/detach-teachers', 'ClassesController@detachTeachers')->name('class.detach_teachers');

//    Route::post('classes/change-subject-teacher', 'ClassesController@changeTeacher')->name('class.change_teacher');
    Route::post('classes/change-subject-teacher', 'ChangeClassTeacherController')->name('class.change_teacher');
    Route::post('classes/add-subject-to-class', 'SubjectClassController@addSubjectToClass')->name('class.add_subject_to_class');
//    Route::post('classes/add>-timetable', 'ClassesController@addTimetable')->name('class.add_timetable');
    Route::post('classes/add-timetable', 'ClassTeacherTimetableController@addTimetable')->name('class.add_timetable');
    Route::post('classes/change-timetable/{id}', 'ClassTeacherTimetableController@changeTimetable')->name('class.change_timetable');
    Route::post('classes/change-timetable-day/{id}', 'ClassTeacherTimetableController@changeTimetableDay')->name('class.change_timetable_day');
    Route::post('classes/change-timetable-duration/{id}', 'ClassTeacherTimetableController@changeTimetableDuration')->name('class.change_timetable_duration');
    Route::post('classes/change-timetable-start-date/{id}', 'ClassTeacherTimetableController@changeTimetableStartDate')->name('class.change_timetable_start_date');
    Route::put('/timetables/{timetable}/clear', [\Modules\Education\Http\Controllers\ClassTeacherTimetableController::class, 'clear'])->name('clear.class.timetable.day');

//    Route::post('classes/change-student-class', 'ClassesController@changeStudentClass')->name('class.change_student_class');
    Route::post('classes/change-student-class', 'ChangeStudentClassController')->name('class.change_student_class');
    Route::delete('/class-teacher/remove-teacher-from-class', 'TeacherController@destroy')->name('teacher.class.destroy');

    //
    Route::resource('hefz-program', 'SpecialPrograms\HefzController');

    Route::get('test-plan', 'SpecialPrograms\HefzController@testStudentPlan');
    Route::post('program-levels/add_program_level_subject', 'ProgramLevelsController@add_program_level_subject')->name('program-levels.add_program_level_subject');
    Route::post('ijazasanad/level/{levelId}/update-talqeen/', 'IjazaSanadLevel1TalqeenController@update')->name('ijazasanad-level1-update-talqeen');
    Route::post('ijazasanad/level/{levelId}/update-revision/', 'IjazaSanadLevel1RevisionController@update')->name('ijazasanad-level1-update-revision');
    Route::post('ijazasanad/level/{levelId}/update-jazariyah/', 'IjazaSanadLevel1JazariyahController@update')->name('ijazasanad-level1-update-jazariyah');
    Route::post('ijazasanad/level/{levelId}/update-seminars/', 'IjazaSanadLevel1SeminarsController@update')->name('ijazasanad-level1-update-seminars');

    Route::delete('program-levels/delete_program_level_subject/{id}', 'ProgramLevelsController@delete_program_level_subject')->name('program-levels.delete_program_level_subject');


    Route::get('surah/{surah}/get-ayats/{hefzPlan?}', function (Request $request, $surahId, $hefzPlanId) {


        $surah = \App\MoshafSurah::find($surahId);

        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);

        //get the ayat lit based on the monthly plan
//        \App\StudentHefzPlan::where('student_id',$studentId)
        $options = '<option value="" selected>Select</option>';

        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';
            }
        }

        return $options;
    });
    Route::get('from-surah/{surah}/get-ayats/{hefzPlan?}', function (Request $request, $surahId, $hefzPlanId) {

        $fromSurah = \App\MoshafSurah::find($surahId);

        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);

        $options = '<option value="" selected>Select</option>';



        if ($fromSurah->id == $hefzPlan->to_surat) {

            $fromSurahNum_ayat = $hefzPlan->to_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
//                if ($i >= $hefzPlan->start_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                $options .= '<option value="' . $i . '">' . $i . '</option>';
            }
        } else if($fromSurah->id == $hefzPlan->start_from_surat) {


            $fromSurahNum_ayat = $fromSurah->num_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {


//                if ($i >= $hefzPlan->start_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                if ($i >= $hefzPlan->start_from_ayat) { // this condition applies to same surah ( from and to surah)


                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }
        }
        }


        else {





                $fromSurahNum_ayat = $fromSurah->num_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
                for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {




                    $options .= '<option value="' . $i . '">' . $i . '</option>';



            }
        }


        return $options;
    });
    Route::get('from-surah/{surah}/ijazasanad/get-ayats/{hefzPlan?}', function (\Illuminate\Http\Request $request, $surahId, $hefzPlanId) {

        $fromSurah = \App\MoshafSurah::find($surahId);

        // Retrieve 'from_date' from the request
        $fromDate = $request->get('from_date');
        $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);
        // Query the reports with the specified 'hefz_plan_id' and 'from_date'
        $reports = \App\StudentIjazasanadMemorizationReport::where('hefz_plan_id', $hefzPlanId)
            ->whereDate('created_at', $fromDate)
            ->first();

        $options = '<option value="" selected>Select</option>';





        if ($fromSurah->id == $hefzPlan->to_surat) {

            $i = $reports->hefz_from_ayat ?? $hefzPlan->start_from_ayat;
            $fromSurahNum_ayat = $hefzPlan->to_ayat;
            for ($i = $i; $i <= $fromSurahNum_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';
            }
        } else if($fromSurah->id == $hefzPlan->start_from_surat) {



            $fromSurahNum_ayat = $fromSurah->num_ayat;

            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {


                if ($i >= $hefzPlan->start_from_ayat) {


                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }
        }
        }


        else {
                $fromSurahNum_ayat = $fromSurah->num_ayat;
                for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';



            }
        }


        return $options;
    });
    Route::get('to-surah/{surah}/get-ayats/hefz-plan/{hefzPlan}/hefz-report-id/{reportID}', function (Request $request, $toSurahId, $hefzPlanId, $hefzReportId) {


        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);
        $hefzReport = \App\StudentHefzReport::find($hefzReportId);
        $options = '<option value="" selected>Select</option>';
        if ($hefzPlan->study_direction == 'forward') {


            if ($hefzReport->hefz_from_surat == $toSurah->id) { // if from_surat == to_surat

                // check if the from surah == hefzPlan To Surat
                if ($toSurah->id == $hefzPlan->to_surat) {
                    $toSurahNum_ayat = $hefzPlan->to_ayat;
                    for ($i = 1; $i <= $toSurahNum_ayat; $i++) {
                        if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }

                    }
                } else {
                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                        if ($i > $hefzReport->hefz_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }
            } // if the from and to surats are not similar
            else {
                // if to surat == hefzPlan->toSurat, then apply this rule
                if ($hefzReport->hefz_from_surat == $hefzPlan->to_surat) {
                    for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                } else {
                    if ($toSurahId == $hefzPlan->to_surat) {
                        for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    } else {
                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';

                        }
                    }
                }
            }
        }
        if ($hefzPlan->study_direction == 'backward') {
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i > $hefzReport->hefz_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                    $options .= '<option value="' . $i . '">' . $i . '</option>';
//                    }
                }
            }
        }

        return $options;
    });

    Route::get('to-surah/{surah}/ijazasanad/get-ayats/hefz-plan/{hefzPlan}/hefz-report-id/{reportID}', function (Request $request, $toSurahId, $hefzPlanId, $hefzReportId) {


        $toSurah = \App\MoshafSurah::find($toSurahId);
        $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);
        $hefzReport = \App\StudentIjazasanadMemorizationReport::find($hefzReportId);
        $options = '<option value="" selected>Select</option>';
        if ($hefzPlan->study_direction == 'forward') {

            if ($hefzReport->hefz_from_surat == $toSurah->id) { // if from_surat == to_surat

                // check if the from surah == hefzPlan To Surat
                if ($toSurah->id == $hefzPlan->to_surat) {
                    $toSurahNum_ayat = $hefzPlan->to_ayat;
                    for ($i = 1; $i <= $toSurahNum_ayat; $i++) {
                        if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }

                    }
                } else {
                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                        if ($i > $hefzReport->hefz_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    }
                }
            } // if the from and to surats are not similar
            else {
                // if to surat == hefzPlan->toSurat, then apply this rule
                if ($hefzReport->hefz_from_surat == $hefzPlan->to_surat) {
                    for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }
                } else {
                    if ($toSurahId == $hefzPlan->to_surat) {
                        for ($i = 1; $i <= $hefzPlan->to_ayat; $i++) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';
                        }
                    } else {
                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';

                        }
                    }
                }
            }
        }
        if ($hefzPlan->study_direction == 'backward') {
            if ($hefzReport->hefz_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i > $hefzReport->hefz_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }

                }
            } // if the from and to surats are not similar
            else {

                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';
//                    }
                }
            }
        }

        return $options;
    });
    Route::post('/lessons/update-order', [\Modules\Education\Http\Controllers\LessonController::class, 'updateLessonOrder'])->name('lessons.updateOrder');
    Route::post('/level-3-talaqqi-lessons/update-order', [\Modules\Education\Http\Controllers\LessonController::class, 'updateLevelThreeTalaqqiLessonOrder'])->name('lessons.updateLevelThreeTalaqqiLessonOrder');
    Route::post('/level-3-talqeen-lessons/update-order', [\Modules\Education\Http\Controllers\LessonController::class, 'updateLevelThreeTalqeenLessonOrder'])->name('lessons.updateLevelThreeTalqeenLessonOrder');


    Route::get('get-to-surat/from-surat/{surah}/hefz-plan/{hefzPlan?}', function (Request $request, $surahId, $hefzPlanId) {

//        $fromSurah = \App\MoshafSurah::find($surahId);

        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);

        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);
        $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();


        $options = '<option value="" selected>Select</option>';

        if ($hefzPlan->study_direction == 'backward') {


            foreach ($allSurahs as $surat) {


                if ($surat->id <= $surahId) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';


//                    } else {
//
//                        $options .= '<option disabled readonly >' . $surat->name . '</option>';


                }

            }

        }

        if ($hefzPlan->study_direction == 'forward') {


            foreach ($allSurahs as $surat) {


                if ($surat->id >= $surahId) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';


//                    } else {
//
//                        $options .= '<option disabled readonly >' . $surat->name . '</option>';


                }

            }

        }


        return $options;
    });
    Route::get('get-to-surat/ijazasanad/from-surat/{surah}/hefz-plan/{hefzPlan?}', function (Request $request, $surahId, $hefzPlanId) {

//        $fromSurah = \App\MoshafSurah::find($surahId);

        $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);

        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);
        $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();


        $options = '<option value="" selected>Select</option>';

        if ($hefzPlan->study_direction == 'backward') {


            foreach ($allSurahs as $surat) {


                if ($surat->id <= $surahId) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';

                }

            }

        }

        if ($hefzPlan->study_direction == 'forward') {


            foreach ($allSurahs as $surat) {
                if ($surat->id >= $surahId) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';

                }

            }

        }


        return $options;
    });
    Route::get('get-to-lesson/from-lesson/{lesson}/nouranya-plan/{nouranyaPlan?}', function (Request $request, $lessonId, $nouranyaPlanId) {


        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);
        $monthlyPlanLessons = range($lessonId+1, $nouranyaPlan->to_lesson);
        $allLessons = \App\ProgramLevelLesson::where('program_level_id',$nouranyaPlan->level_id)->whereIn('id',$monthlyPlanLessons)->get();

        $options = '<option value="" selected>Select</option>';
        foreach ($allLessons as $lesson) {
//            $lessonId = $lesson['id'];
            $lessonName = $lesson->properties['name'] ?? ($lesson->properties['letter'] ?? ($lesson->properties['title'] ?? ''));
//            $selected = $currentLessonId == $lessonId ? 'selected' : '';

            $options .= '<option value="' . htmlspecialchars($lessonId, ENT_QUOTES, 'UTF-8') . '" >'
                . htmlspecialchars($lessonName, ENT_QUOTES, 'UTF-8') .
                '</option>';
        }
        return $options;
    });
    Route::get('talaqqi-get-to-lesson/from-lesson/{lesson}/nouranya-plan/{nouranyaPlanId?}', function (Request $request, $lessonNo, $nouranyaPlanId) {
        // Fetch the Nouranya plan using the provided ID
        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);

        // Define the maximum lesson number from the Nouranya plan
        $maxTalaqqiLessonNo = optional($nouranyaPlan)->talaqqi_to_lesson;

        // Determine the available lessons based on talaqqi type and max lesson constraint
        $availableToLessons = \App\Talaqqi::where('lesson_no', '>=', (int)$lessonNo)
            ->when($maxTalaqqiLessonNo, function ($query) use ($maxTalaqqiLessonNo) {
                return $query->where('lesson_no', '<=', (int)$maxTalaqqiLessonNo);
            })
            ->orderBy(DB::raw('CAST(lesson_no AS UNSIGNED)'), 'asc')
            ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
            ->map(function ($lesson) {
                return [
                    'id' => $lesson->id,
                    'lesson_no' => $lesson->lesson_no,
                    'name' => \App\MoshafSurah::find($lesson->to_surah_id)->eng_name . ':' . $lesson->from_ayah_number . '-' . $lesson->to_ayah_number,
                    'created_at' => $lesson->created_at,
                    'updated_at' => $lesson->updated_at,
                ];
            })->sortBy('lesson_no', SORT_NATURAL);

        // Generate the HTML options for the dropdown
        $options = '<option value="" selected>Select To Lesson</option>';
        foreach ($availableToLessons as $lesson) {
            $options .= '<option value="' . $lesson['lesson_no'] . '">' . $lesson['lesson_no'] . '. ' . $lesson['name'] . '</option>';
        }

        return $options;
    });
    Route::get('talqeen-get-to-lesson/from-lesson/{lesson}/nouranya-plan/{nouranyaPlanId?}', function (Request $request, $lessonNo, $nouranyaPlanId) {
        // Fetch the Nouranya plan using the provided ID
        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);

        // Define the maximum lesson number from the Nouranya plan
        $maxTalqeenLessonNo = optional($nouranyaPlan)->talqeen_to_lesson;

        // Determine the available lessons based on talqeen type and max lesson constraint
        $availableToLessons = \App\Talqeen::where('lesson_no', '>=', (int)$lessonNo)
            ->when($maxTalqeenLessonNo, function ($query) use ($maxTalqeenLessonNo) {
                return $query->where('lesson_no', '<=', (int)$maxTalqeenLessonNo);
            })
            ->orderBy(DB::raw('CAST(lesson_no AS UNSIGNED)'), 'asc')
            ->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
            ->map(function ($lesson) {
                return [
                    'id' => $lesson->id,
                    'lesson_no' => $lesson->lesson_no,
                    'name' => \App\MoshafSurah::find($lesson->from_surah_id)->eng_name . ':' . $lesson->from_ayah_number . '-' . \App\MoshafSurah::find($lesson->to_surah_id)->eng_name . '-' . $lesson->to_ayah_number,
                    'created_at' => $lesson->created_at,
                    'updated_at' => $lesson->updated_at,
                ];
            });

        // Generate the HTML options for the dropdown
        $options = '<option value="" selected>Select To Lesson</option>';
        foreach ($availableToLessons as $lesson) {
            $options .= '<option value="' . $lesson['lesson_no'] . '">' . $lesson['lesson_no'] . '. ' . $lesson['name'] . '</option>';
        }

        return $options;
    });
    Route::get('get-to-surat/hefz-report/{reportId}/hefz-plan/{hefzPlanId?}', [\Modules\Education\Http\Controllers\HefzReportToSuratOptionsController::class, 'getToSuratOptions']);
    Route::get('get-to-surat/hefz-report/{reportId}/ijazasanad/hefz-plan/{hefzPlanId?}', [\Modules\Education\Http\Controllers\IjazasanadMemorizationReportToSuratOptionsController::class, 'getToSuratOptions']);

    Route::get('get-from-surat/hefz-plan/{hefzPlan?}', function (Request $request, $hefzPlanId) {


        $hefzPlan = \App\StudentHefzPlan::find($hefzPlanId);

        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);

//        if ($hefzPlan->study_direction == 'backward') {
//
//            $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->orderBy('id', 'desc')->get();
//
//        } else {

            $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();

//        }


        $options = '<option value="" selected>Select</option>';


        foreach ($allSurahs as $surat) {


            $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';


//                    } else {
//
//                        $options .= '<option disabled readonly >' . $surat->name . '</option>';


        }


        return $options;
    });
    Route::get('get-from-surat/ijazasanad/hefz-plan/{hefzPlan?}', function (Request $request, $hefzPlanId) {


        $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);

        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);


            $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();


        $options = '<option value="" selected>Select</option>';


        foreach ($allSurahs as $surat) {


            $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';


//                    } else {
//
//                        $options .= '<option disabled readonly >' . $surat->name . '</option>';


        }


        return $options;
    });
    Route::get('get-from-surat/ijazasanad/hefz-plan/{hefzPlan?}', function (Request $request, $hefzPlanId) {


        $hefzPlan = \App\IjazasanadMemorizationPlan::find($hefzPlanId);

        $monthlyPlanSurats = range($hefzPlan->start_from_surat, $hefzPlan->to_surat);



            $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();

        $options = '<option value="" selected>Select</option>';


        foreach ($allSurahs as $surat) {
            $options .= '<option value="' . $surat->id . '">' . $surat->id . '. ' . $surat->name . '</option>';
        }


        return $options;
    });
    Route::get('get-from-lesson/nouranya-plan/{nouranyaPlan?}', function (Request $request, $nouranyaPlanId) {
        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);
        $monthlyPlanLessons = range($nouranyaPlan->from_lesson, $nouranyaPlan->to_lesson);
            $allLessons = \App\ProgramLevelLesson::where('program_level_id',$nouranyaPlan->level_id)->whereIn('id',$monthlyPlanLessons)->get();
            $currentLessonId = $nouranyaPlan->from_lesson;

            $options = '<option value="" selected>Select</option>';
        foreach ($allLessons as $lesson) {
            $lessonId = $lesson['id'];
            $lessonName = $lesson->properties['name'] ?? ($lesson->properties['letter'] ?? ($lesson->properties['title'] ?? ''));

            $options .= '<option value="' . htmlspecialchars($lessonId, ENT_QUOTES, 'UTF-8') . '">'
                . htmlspecialchars($lessonName, ENT_QUOTES, 'UTF-8') .
                '</option>';
        }
        return $options;
    });

    Route::get('get-talaqqi-from-lesson/nouranya-plan/{nouranyaPlan?}', function (Request $request, $nouranyaPlanId) {
        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);
        $monthlyPlanLessons = range($nouranyaPlan->from_lesson, $nouranyaPlan->to_lesson);




        $talaqqiTalqeenType = $nouranyaPlan->talqeen_talaqqi;



            // Determine the available lessons based on talaqqi or talqeen type
            $allLessons = $talaqqiTalqeenType === 'talaqqi'
                ? \App\Talaqqi::whereIn('id',$monthlyPlanLessons)->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => \App\MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    })
                : \App\Talqeen::whereIn('id',$monthlyPlanLessons)->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => \App\MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });


            $options = '<option value="" selected>Select</option>';
        foreach ($allLessons as $lesson) {
            $lessonId = $lesson['id'];
            $lessonName = $lesson->properties['name'] ?? ($lesson->properties['letter'] ?? ($lesson->properties['title'] ?? ''));

            $options .= '<option value="' . htmlspecialchars($lessonId, ENT_QUOTES, 'UTF-8') . '">'
                . htmlspecialchars($lessonName, ENT_QUOTES, 'UTF-8') .
                '</option>';
        }
        return $options;
    });
    Route::get('get-talqeen-from-lesson/nouranya-plan/{nouranyaPlan?}', function (Request $request, $nouranyaPlanId) {
        $nouranyaPlan = \App\StudentNouranyaPlan::find($nouranyaPlanId);
        $monthlyPlanLessons = range($nouranyaPlan->from_lesson, $nouranyaPlan->to_lesson);




        $talaqqiTalqeenType = $nouranyaPlan->talqeen_talaqqi;


            // Determine the available lessons based on talaqqi or talqeen type
            $allLessons = $talaqqiTalqeenType === 'talaqqi'
                ? \App\Talaqqi::whereIn('id',$monthlyPlanLessons)->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => \App\MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    })
                : \App\Talqeen::whereIn('id',$monthlyPlanLessons)->get(['id', 'lesson_no', 'program_id', 'from_surah_id', 'from_ayah_number', 'to_surah_id', 'to_ayah_number', 'created_at', 'updated_at'])
                    ->map(function ($lesson) {
                        return [
                            'id' => $lesson->id,
                            'lesson_no' => $lesson->lesson_no,
                            'name' => \App\MoshafSurah::find($lesson->from_surah_id)->name . '-' . $lesson->from_ayah_number,
                            'created_at' => $lesson->created_at,
                            'updated_at' => $lesson->updated_at,
                        ];
                    });



            $options = '<option value="" selected>Select</option>';
        foreach ($allLessons as $lesson) {
            $lessonId = $lesson['id'];
            $lessonName = $lesson->properties['name'] ?? ($lesson->properties['letter'] ?? ($lesson->properties['title'] ?? ''));
            $selected = $currentLessonId == $lessonId ? 'selected' : '';

            $options .= '<option value="' . htmlspecialchars($lessonId, ENT_QUOTES, 'UTF-8') . '" ' . $selected . '>'
                . htmlspecialchars($lessonName, ENT_QUOTES, 'UTF-8') .
                '</option>';
        }
        return $options;
    });

    Route::get('revision/get-to-surat/revision-report/{reportId}/revision-plan/{revisionPlan?}', function (Request $request, $reportId, $revisionPlanId) {


        $revisionPlan = \App\StudentRevisionPlan::find($revisionPlanId);
        $monthlyPlanSurats = range($revisionPlan->start_from_surat, $revisionPlan->to_surat);


        $revisionReport = \App\StudentRevisionReport::find($reportId);


        $options = '<option value="" selected>Select</option>';

        $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();

        if ($revisionPlan->study_direction == 'backward') {
            foreach ($allSurahs as $surat) {
                if ($surat->id <= $revisionReport->revision_from_surat) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . ' . ' . $surat->name . '</option>';

                }
            }
        } else {
            if ($revisionPlan->study_direction == 'forward') {
                foreach ($allSurahs as $surat) {
                    if ($surat->id >= $revisionReport->revision_from_surat) {
                        $options .= '<option value="' . $surat->id . '">' . $surat->id . ' . ' . $surat->name . '</option>';



                    }

                }
            }
        }


        return $options;
    });
    Route::get('revision/get-to-surat/ijazasanad/revision-report/{reportId}/revision-plan/{revisionPlan?}', function (Request $request, $reportId, $revisionPlanId) {


        $revisionPlan = \App\IjazasanadRevisionPlan::find($revisionPlanId);
        $monthlyPlanSurats = range($revisionPlan->start_from_surat, $revisionPlan->to_surat);


        $revisionReport = \App\StudentIjazasanadRevisionReport::find($reportId);




        $options = '<option value="" selected>Select</option>';

        $allSurahs = \App\MoshafSurah::whereIn('id', $monthlyPlanSurats)->get();

        if ($revisionPlan->study_direction == 'backward') {
            foreach ($allSurahs as $surat) {
                if ($surat->id <= $revisionReport->revision_from_surat) {
                    $options .= '<option value="' . $surat->id . '">' . $surat->id . ' . ' . $surat->name . '</option>';

                }
            }
        } else {
            if ($revisionPlan->study_direction == 'forward') {
                foreach ($allSurahs as $surat) {
                    if ($surat->id >= $revisionReport->revision_from_surat) {
                        $options .= '<option value="' . $surat->id . '">' . $surat->id . ' . ' . $surat->name . '</option>';



                    }

                }
            }
        }


        return $options;
    });
    Route::get('revision/from-surah/{surah}/get-ayats/{revsionPlan?}', function (Request $request, $surahId, $revisionPlanId) {


        $fromSurah = \App\MoshafSurah::find($surahId);

        $revisionPlan = \App\StudentRevisionPlan::find($revisionPlanId);

        $options = '<option value="" selected>Select</option>';



        if ($fromSurah->id == $revisionPlan->to_surat) {

            $fromSurahNum_ayat = $revisionPlan->to_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
//                if ($i >= $hefzPlan->start_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                $options .= '<option value="' . $i . '">' . $i . '</option>';
            }
        } else if($fromSurah->id == $revisionPlan->start_from_surat) {


            $fromSurahNum_ayat = $fromSurah->num_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {


//                if ($i >= $hefzPlan->start_from_ayat && $i <= $hefzPlan->to_ayat) { // this condition applies to same surah ( from and to surah)
                if ($i >= $revisionPlan->start_from_ayat) { // this condition applies to same surah ( from and to surah)


                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }
            }
        }


        else {





            $fromSurahNum_ayat = $fromSurah->num_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {




                $options .= '<option value="' . $i . '">' . $i . '</option>';



            }
        }



        return $options;


        $fromSurah = \App\MoshafSurah::find($surahId);
        $revisionPlan = \App\StudentRevisionPlan::find($revisionPlanId);
        $options = '<option value="" selected>Select</option>';

        $fromSurahNum_ayat = $fromSurah->num_ayat;
        for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            $options .= '<option value="' . $i . '">' . $i . '</option>';


        }


        return $options;
    });
    Route::get('revision/from-surah/{surah}/ijazasanad/get-ayats/{revsionPlan?}', function (Request $request, $surahId, $revisionPlanId) {

        $fromSurah = \App\MoshafSurah::find($surahId);
        $revisionPlan = \App\IjazasanadRevisionPlan::find($revisionPlanId);
        $options = '<option value="" selected>Select</option>';
        if ($fromSurah->id == $revisionPlan->to_surat) {
            $fromSurahNum_ayat = $revisionPlan->to_ayat;
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';
            }
        } else if($fromSurah->id == $revisionPlan->start_from_surat) {
            $fromSurahNum_ayat = $fromSurah->num_ayat;
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
                if ($i >= $revisionPlan->start_from_ayat) { // this condition applies to same surah ( from and to surah)


                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }
            }
        }


        else {





            $fromSurahNum_ayat = $fromSurah->num_ayat;
//            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {
            for ($i = 1; $i <= $fromSurahNum_ayat; $i++) {




                $options .= '<option value="' . $i . '">' . $i . '</option>';



            }
        }



        return $options;
    });

    Route::get('revision-report/{revisionReportId}/to-surah/{surah}/get-ayats/revision-plan/{hefzPlan}/revision-id/{reportID}', function (Request $request, $revisionReportId, $toSurahId, $hefzPlanId, $revisionId) {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $revisionPlan = \App\StudentRevisionPlan::find($hefzPlanId);
        $hefzRevisionReport = \App\StudentRevisionReport::find($revisionId);
        $options = '<option value="" selected>Select</option>';


        if ($revisionPlan->study_direction == 'forward') {


//            if ($revisionPlan->start_from_surat == $toSurah->id) {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                if ($toSurah->id == $revisionPlan->to_surat) {
                    $toSurahNum_ayat = $revisionPlan->to_ayat;

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    for ($i = 1; $i <= $toSurahNum_ayat; $i++) {

                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }


                    }
                } else {

                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                        if ($i > $hefzRevisionReport->revision_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }


                    }

                }

            } // if the from and to surats are not similar


            else {


                // if to surat == revisionPlan->toSurat, then apply this rule
                if ($hefzRevisionReport->revision_from_surat == $revisionPlan->to_surat) {

                    for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }
                } else {
                    if ($toSurahId == $revisionPlan->to_surat) {

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    } else {

                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    }


                }
            }
        }

        if ($revisionPlan->study_direction == 'backward') {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i > $hefzRevisionReport->revision_from_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }

                }
            } // if the from and to surats are not similar
            else {


                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
//                    }

                }
            }
        }
        return $options;
        if (isset($toSurah)) {
            $revisionReport = \App\StudentRevisionReport::find($revisionReportId);


            $num_ayat = $toSurah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {


                if ($revisionReport->revision_from_surat === $toSurah->id) {
                    if ($i > $revisionReport->revision_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }
                } else {

                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }


            }
        }


        return $options;

    });
    Route::get('revision-report/{revisionReportId}/to-surah/{surah}/ijazasanad/get-ayats/revision-plan/{hefzPlan}/revision-id/{reportID}', function (Request $request, $revisionReportId, $toSurahId, $hefzPlanId, $revisionId) {

        $toSurah = \App\MoshafSurah::find($toSurahId);
        $revisionPlan = \App\IjazasanadRevisionPlan::find($hefzPlanId);
        $hefzRevisionReport = \App\StudentIjazasanadRevisionReport::find($revisionId);
        $options = '<option value="" selected>Select</option>';


        if ($revisionPlan->study_direction == 'forward') {


//            if ($revisionPlan->start_from_surat == $toSurah->id) {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                if ($toSurah->id == $revisionPlan->to_surat) {
                    $toSurahNum_ayat = $revisionPlan->to_ayat;

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    for ($i = 1; $i <= $toSurahNum_ayat; $i++) {

                        if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }


                    }
                } else {

                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                if ($toSurahId == $hefzReport->hefz_from_surat) { // if from_surat and to_surats are the same ( ex: al-fatiha to al-fatiha)
//                    if ($i > $hefzReport->hefz_from_ayat && $i <= $hefzPlan->to_ayat) {
                        if ($i > $hefzRevisionReport->revision_from_ayat) {
                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }


                    }

                }

            } // if the from and to surats are not similar


            else {


                // if to surat == revisionPlan->toSurat, then apply this rule
                if ($hefzRevisionReport->revision_from_surat == $revisionPlan->to_surat) {

                    for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                        $options .= '<option value="' . $i . '">' . $i . '</option>';


                    }
                } else {
                    if ($toSurahId == $revisionPlan->to_surat) {

//                    for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                        for ($i = 1; $i <= $revisionPlan->to_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    } else {

                        for ($i = 1; $i <= $toSurah->num_ayat; $i++) {


                            $options .= '<option value="' . $i . '">' . $i . '</option>';


                        }

                    }


                }
            }
        }

        if ($revisionPlan->study_direction == 'backward') {
            if ($hefzRevisionReport->revision_from_surat == $toSurah->id) {
                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
                    if ($i > $hefzRevisionReport->revision_from_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
                    }

                }
            } // if the from and to surats are not similar
            else {


                for ($i = 1; $i <= $toSurah->num_ayat; $i++) {
//                    if ($i > $hefzRevisionReport->revision_from_ayat && $i <= $revisionPlan->to_ayat) {

                        $options .= '<option value="' . $i . '">' . $i . '</option>';
//                    }

                }
            }
        }
        return $options;


    });


    // revision monthly Plan

    Route::get('revision-monthly-plan/from-surah/{surah}/get-ayats', function (Request $request, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition


        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';

            }
        }

        return $options;
    });
    Route::get('revision-monthly-plan/{revisionMonthlyPlanId}/to-surah/{surah}/get-ayats', function (Request $request, $revisionMonthlyPlanId, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition

        if (isset($surah)) {
            $revisionPlan = \App\StudentRevisionPlan::find($revisionMonthlyPlanId);


            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {


                if ($revisionPlan->start_from_surat === $surah->id) {
                    if ($i > $revisionPlan->start_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }
                } else {

                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }


            }
        }

        return $options;
    });


    // Ijazasanad Revision monthly plan
    Route::get('revision-monthly-plan/ijazasanad/from-surah/{surah}/get-ayats', function (Request $request, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition


        if (isset($surah)) {
            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {
                $options .= '<option value="' . $i . '">' . $i . '</option>';

            }
        }

        return $options;
    });
    Route::get('revision-monthly-plan/ijazasanad/{revisionMonthlyPlanId}/to-surah/{surah}/get-ayats', function (Request $request, $revisionMonthlyPlanId, $surah) {
        $surah = \App\MoshafSurah::find($surah);
        $options = '<option value="" selected>Select</option>';


//        if there is already a hefzPlan record details, then get the from surat and from ayat details, else do another condition

        if (isset($surah)) {
            $revisionPlan = \App\IjazasanadRevisionPlan::class::find($revisionMonthlyPlanId);


            $num_ayat = $surah->num_ayat;
            for ($i = 1; $i <= $num_ayat; $i++) {


                if ($revisionPlan->start_from_surat === $surah->id) {
                    if ($i > $revisionPlan->start_from_ayat) {
                        $options .= '<option value="' . $i . '">' . $i . '</option>';

                    }
                } else {

                    $options .= '<option value="' . $i . '">' . $i . '</option>';

                }


            }
        }

        return $options;
    });

    Route::get('upload-content-edit/{id}', 'TeacherController@uploadContentEdit')->name('upload-content-edit');
    Route::get('upload-content-view/{id}', 'TeacherController@uploadContentView')->name('upload-content-view');
    Route::get('/get-centers', 'GetCentersController')->name('education.get.centers');
    Route::get('/get-classes', 'GetClassesController')->name('education.get.classes');
    Route::get('/get-program-levels', 'GetProgramLevelsController')->name('education.get.program.levels');
    Route::get('/get-program-levels-datatable', 'GetProgramLevelsDataTableController')->name('education.get.program.levels.datatable');
    Route::post('/update-program-level-order', 'UpdateProgramLevelOrderController')->name('update.program.level.order');


    Route::get('classes-timetables', 'ClassesTimetablesController@index');

    // Nouranya Report Routes (single-class)
    Route::get('/monthly-nouranya-report', MonthlyNouranyaReportController::class)->name('monthly-nouranya-report');
    Route::get('/month-end-nouranya-summary-report', MonthEndNouranyaSummaryController::class)->name('month-end-nouranya-summary-report');

    // Ijazasanad Report Routes (single-class)
    Route::get('/monthly-ijazasanad-report', MonthlyIjazasanadReportController::class)->name('monthly-ijazasanad-report');
    Route::get('/month-end-ijazasanad-summary-report', MonthEndIjazasanadSummaryController::class)->name('month-end-ijazasanad-summary-report');
    Route::get('/student-ijazasanad-reports/students', [StudentIjazasanadReportsDatatablesController::class, 'getStudents'])->name('student-ijazasanad-reports.students');
    Route::get('/student-ijazasanad-reports/reports', [StudentIjazasanadReportsDatatablesController::class, 'getStudentReports'])->name('student-ijazasanad-reports.reports');
    Route::get('/student-ijazasanad-reports/summary', [StudentIjazasanadReportsDatatablesController::class, 'getStudentSummary'])->name('student-ijazasanad-reports.summary');
    Route::get('classes/{id}/report/ijazasanad', [IjazasanadMonthYearController::class, 'getClassReportData'])->name('class.ijazasanad.report');
    
    // Add Ijazasanad routes to match Nouranya pattern
    Route::get('class/student-ijazasanad-report', [StudentIjazasanadReportsDatatablesController::class, 'studentRecords'])->name('class.student-ijazasanad-report');
    Route::get('class/student-ijazasanad-month-years/{studentId}/{classId}', [IjazasanadMonthYearController::class, 'getMonthYears'])->name('student.ijazasanad.month-years');
    Route::get('class/month-end-ijazasanad-student-summary-report/{classId}', [MonthEndIjazasanadStudentSummaryController::class, '__invoke'])->name('month.end.student.ijazasanad.summary.report');
    

    
    Route::get('/student-ijazasanad-report', [ClassIjazasanadReportController::class, 'studentReport'])->name('student.ijazasanad.report');

    Route::get('/revision-remarks/{reportId}', function ($reportId) {

        $report = \App\StudentRevisionReport::find($reportId);

        if ($report) {
            return response()->json(['revision_evaluation_note' => $report->revision_evaluation_note]);
        } else {
            return response()->json(['message' => 'Report not found'], 404);
        }

    });

    Route::get('classes/{id}/nouranya/month-end-student-summary-report', 'MonthEndNouranyaStudentSummaryController')->name('month.end.student.nouranya.summary.report')->middleware('role:administrative_2_|managing-director_2_|it-officer_2_|teacher_2_|education-assistant_2_');







    // Bulk operations for students with missing data
    Route::post('/students/bulk-assign', 'StudentMissingDataController@bulkAssign')->name('students.bulk-assign')->middleware('permission:update student');
    Route::post('/students/bulk-create-users', 'StudentMissingDataController@bulkCreateUsers')->name('students.bulk-create-users')->middleware('permission:add user');

    // Interactive user creation for students with active admissions
    Route::post('/students/create-user-for-admission', 'StudentMissingDataController@createUserForAdmission')->name('admin.student-missing-data.create-user-for-admission')->middleware('permission:add user');

    // Individual update routes for students with missing data
    Route::put('/students/{studentId}/update-gender', 'StudentMissingDataController@updateIndividualGender')->name('students.update-individual-gender')->middleware('permission:update student');
    Route::put('/students/{studentId}/update-dob', 'StudentMissingDataController@updateIndividualDateOfBirth')->name('students.update-individual-dob')->middleware('permission:update student');
    Route::put('/students/{studentId}/update-full-name', 'StudentMissingDataController@updateIndividualFullName')->name('students.update-individual-full-name')->middleware('permission:update student');
    Route::put('/students/{studentId}/update-nationality', 'StudentMissingDataController@updateIndividualNationality')->name('students.update-individual-nationality')->middleware('permission:update student');
    Route::post('/students/{studentId}/update-image', 'StudentMissingDataController@updateIndividualImage')->name('students.update-individual-image')->middleware('permission:update student');

    // Email sending routes for user credentials
    Route::post('/students/send-credentials-email', 'StudentMissingDataController@sendCredentialsEmail')->name('students.send-credentials-email')->middleware('permission:add user');
    Route::post('/students/send-all-credentials-emails', 'StudentMissingDataController@sendAllCredentialsEmails')->name('students.send-all-credentials-emails')->middleware('permission:add user');

    // Export route for students with missing data
    Route::post('/students/export-missing-data', 'StudentMissingDataController@exportToExcel')->name('admin.student-missing-data.export-excel')->middleware('permission:view student');
    Route::get('/students/export-missing-dataa', 'StudentMissingDataController@exportToExcel')->name('students.export-missing-data')->middleware('permission:view student');

});
