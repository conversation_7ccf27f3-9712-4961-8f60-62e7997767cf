<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Log;
use <PERSON><PERSON><PERSON>\JobSeeker\Services\CategoryMappingService;
use <PERSON><PERSON>les\JobSeeker\Services\JobNotificationHub;
use <PERSON><PERSON><PERSON>\JobSeeker\Services\MissedJobService;
use <PERSON><PERSON><PERSON>\JobSeeker\Services\NotificationProcessLogger;
use Mo<PERSON>les\JobSeeker\Services\JobNotificationMonitoringService;

/**
 * JobNotificationService serves as the unified entrypoint for job notifications
 * across all provider services with comprehensive monitoring and decision audit trail.
 *
 * Purpose: Provider-agnostic notification orchestration with complete observability.
 * Inputs: Job arrays with provider_category_ids, schedule context with trace_id/execution_id.
 * Outputs: Boolean success indicator, comprehensive process logs, failure tracking.
 * Side effects: Maps categories via CategoryMappingService, logs to notification_process_logs,
 * tracks failures in job_notification_failures, delegates to JobNotificationHub.
 *
 * Monitoring Infrastructure:
 * - NotificationProcessLogger: End-to-end process tracking with provider context
 * - JobNotificationMonitoringService: Category mapping failure analysis
 * - CategoryMappingService integration: Decision audit trail for mapping results
 * - Comprehensive error handling: All mapping and processing failures captured
 *
 * Key Responsibilities:
 * - Accept jobs and provider context from any provider service with full logging
 * - Map provider-specific categories to canonical categories with decision tracking
 * - Track mapping success rates and unmapped category patterns
 * - Group jobs by canonical categories with comprehensive validation
 * - Delegate to JobNotificationHub with complete context propagation
 * - CENTRALIZED MISSED CALL DETECTION: Trigger alerts when notifications fail
 * - Maintain full traceability through trace_id and execution_id correlation
 *
 * Error Handling: All exceptions captured with business context including job details,
 * setup information, and provider-specific data for comprehensive troubleshooting.
 *
 * Dependencies: CategoryMappingService (with process logger integration),
 * JobNotificationHub, MissedJobService, JobNotificationMonitoringService.
 *
 * Current Process Flow with Monitoring (Post-Implementation):
 *
 * ```mermaid
 * flowchart TD
 *     A[1 Start: Provider Service Call] --> B[2 JobNotificationService.notifyAggregatedJobs]
 *     B --> C[3 Initialize NotificationProcessLogger]
 *     C --> D[4 Log: notification_service_start]
 *     D --> E[5 CategoryMappingService.setProcessLogger]
 *     E --> F[6 CategoryMappingService.mapProviderToCanonicalIds]
 *     F --> G[7 Log: category_mapping decisions + success rates]
 *     G --> H[8 JobNotificationHub.processAndDeliverNotifications]
 *     H --> I[9 Initialize Hub ProcessLogger + MonitoringService]
 *     I --> J[10 Log: notification_hub_start]
 *     J --> K[11 Group jobs by canonical categories]
 *     K --> L[12 Log: category_grouping with counts]
 *     L --> M[13 Load active setups + Log: user_matching start]
 *     M --> N[14 For each setup: track timing + memory]
 *     N --> O[15 Build unified job list per recipient]
 *     O --> P[16 Log: email_sending with job context]
 *     P --> Q[17 EmailService.send with error handling]
 *     Q --> R[18 Track: email metrics + setup processing metrics]
 *     R --> S[19 Log: email_sending success/failure]
 *     S --> T[20 Record sent jobs + Log: user_matching complete]
 *     T --> U[21 Log: notification_hub_complete]
 *
 *     F -. mapping error .-> V[6.1 MonitoringService.trackFailure]
 *     F -. no mappings .-> W[6.2 Log: category_mapping_gaps]
 *     Q -. email error .-> X[17.1 MonitoringService.trackFailure + Log error]
 *
 *     V --> Y[Error context: job details + provider info]
 *     W --> Z[Decision audit: unmapped categories + reasons]
 *     X --> AA[Email failure: setup + recipient + job context]
 * ```
 *
 * Monitoring Data Available:
 * - notification_process_logs: Step-by-step execution with business context
 * - job_notification_failures: All errors with full business context
 * - job_notification_health_metrics: Performance metrics and success rates
 * - v_execution_troubleshooting: Single-query execution analysis
 * - v_execution_step_analysis: Detailed step-by-step breakdown
 * - v_execution_failures: Failure analysis with job/setup context
 */
final class JobNotificationService
{
    private CategoryMappingService $categoryMappingService;
    private JobNotificationHub $jobNotificationHub;
    private MissedJobService $missedJobService;
    private JobNotificationMonitoringService $monitoringService;
    private ?NotificationProcessLogger $processLogger = null;

    public function __construct(
        CategoryMappingService $categoryMappingService,
        JobNotificationHub $jobNotificationHub,
        MissedJobService $missedJobService,
        JobNotificationMonitoringService $monitoringService
    ) {
        $this->categoryMappingService = $categoryMappingService;
        $this->jobNotificationHub = $jobNotificationHub;
        $this->missedJobService = $missedJobService;
        $this->monitoringService = $monitoringService;
    }

    /**
     * Unified notification entrypoint for all provider services.
     *
     * Accepts jobs from any provider service, maps provider-specific categories
     * to canonical categories, groups jobs by canonical categories, and delegates
     * to JobNotificationHub for actual email delivery.
     *
     * @param array $newJobs Array of new job arrays with provider_category_ids
     * @param array $updatedJobs Array of updated job arrays with provider_category_ids
     * @param array $missedJobs Array of missed job arrays with provider_category_ids
     * @param array $scheduleContext Context including provider, trace_id, execution_id
     * @return bool True if any notifications were sent
     */
    public function notifyAggregatedJobs(
        array $newJobs,
        array $updatedJobs,
        array $missedJobs,
        array $scheduleContext = []
    ): bool {
        $traceId = $scheduleContext['trace_id'] ?? \Illuminate\Support\Str::uuid()->toString();
        $executionId = $scheduleContext['execution_id'] ?? null;
        $provider = $scheduleContext['provider'] ?? 'unknown';

        // Initialize process logger for comprehensive tracking
        $this->processLogger = new NotificationProcessLogger($traceId, $executionId);

        Log::info('START JobNotificationService: Processing provider jobs for canonical notification', [
            'provider' => $provider,
            'new_jobs' => count($newJobs),
            'updated_jobs' => count($updatedJobs),
            'missed_jobs' => count($missedJobs),
            'trace_id' => $traceId,
            'execution_id' => $executionId,
        ]);

        $this->processLogger->logStep('notification_service_start', 'success', 'start', $provider,
            'Started JobNotificationService processing', [
            'new_jobs_count' => count($newJobs),
            'updated_jobs_count' => count($updatedJobs),
            'missed_jobs_count' => count($missedJobs)
        ]);

        try {
            // Combine all jobs for processing
            $allJobs = array_merge($newJobs, $updatedJobs, $missedJobs);

            if (empty($allJobs)) {
                Log::info('JobNotificationService: No jobs to process', [
                    'provider' => $provider,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
                return false;
            }

            // DEBUG: Log the jobs being processed with their provider_category_ids
            Log::info('JobNotificationService: Jobs received for processing', [
                'provider' => $provider,
                'total_jobs' => count($allJobs),
                'sample_job_provider_ids' => !empty($allJobs) ? ($allJobs[0]['provider_category_ids'] ?? 'MISSING') : 'NO_JOBS',
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            // Map provider categories to canonical categories for all jobs
            $jobsWithCanonicalCategories = $this->mapJobsToCanonicalCategories($allJobs, $provider, $traceId, $executionId);

            // Validate mapping results
            $originalJobCount = count($allJobs);
            $mappedJobCount = count($jobsWithCanonicalCategories);
            
            Log::info('JobNotificationService: Category mapping completed', [
                'provider' => $provider,
                'original_job_count' => $originalJobCount,
                'mapped_job_count' => $mappedJobCount,
                'jobs_lost_in_mapping' => $originalJobCount - $mappedJobCount,
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            if (empty($jobsWithCanonicalCategories)) {
                Log::warning('JobNotificationService: All jobs lost during category mapping', [
                    'provider' => $provider,
                    'original_count' => $originalJobCount,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
                return false;
            }

            // Split mapped jobs back into their original arrays based on indices
            $mappedNewJobs = $this->extractJobsByIndices($jobsWithCanonicalCategories, 0, count($newJobs));
            $mappedUpdatedJobs = $this->extractJobsByIndices($jobsWithCanonicalCategories, count($newJobs), count($updatedJobs));
            $mappedMissedJobs = $this->extractJobsByIndices($jobsWithCanonicalCategories, count($newJobs) + count($updatedJobs), count($missedJobs));

            // Delegate to JobNotificationHub with canonical categories
            $result = $this->jobNotificationHub->processAndDeliverNotifications(
                $mappedNewJobs,
                $mappedUpdatedJobs,
                $mappedMissedJobs,
                $scheduleContext
            );

            // PROVIDER-AGNOSTIC MISSED CALL DETECTION: Centralized logic for all providers
            $totalJobsFound = count($allJobs);
            if ($totalJobsFound > 0 && !$result) {
                $this->triggerMissedCallAlert($newJobs, $updatedJobs, $missedJobs, $scheduleContext);
            }

            Log::info('END JobNotificationService: Canonical notification processing completed', [
                'provider' => $provider,
                'notifications_sent' => $result,
                'total_jobs_processed' => $totalJobsFound,
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);

            return $result;

        } catch (\Throwable $e) {
            // Track critical service failure with comprehensive context
            $this->monitoringService->trackFailure(
                'notification_service_error',
                $e->getMessage(),
                null,
                null,
                null,
                [
                    'provider' => $provider,
                    'new_jobs_count' => count($newJobs),
                    'updated_jobs_count' => count($updatedJobs),
                    'missed_jobs_count' => count($missedJobs),
                    'error_class' => get_class($e),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine()
                ],
                $e
            );

            $this->processLogger->logStep('notification_service_error', 'error', 'error', $provider,
                'JobNotificationService processing failed with exception', [
                'error_message' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine()
            ]);

            Log::error('JobNotificationService: Error during canonical notification processing', [
                'provider' => $provider,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);
            return false;
        }
    }

    /**
     * Maps jobs from provider-specific categories to canonical categories.
     *
     * @param array $jobs Jobs with provider_category_ids
     * @param string $provider Provider identifier
     * @param string|null $traceId Trace ID for logging
     * @param int|null $executionId Execution ID for logging
     * @return array Jobs with canonical_category_ids instead of provider_category_ids
     */
    private function mapJobsToCanonicalCategories(array $jobs, string $provider, ?string $traceId, ?int $executionId): array
    {
        $mappedJobs = [];
        $unmappedJobCount = 0;

        foreach ($jobs as $job) {
            $providerCategoryIds = $job['provider_category_ids'] ?? [];
            
            if (empty($providerCategoryIds)) {
                Log::warning('JobNotificationService: Job missing provider_category_ids', [
                    'job_id' => $job['id'] ?? 'unknown',
                    'provider' => $provider,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
                $unmappedJobCount++;
                continue;
            }

            try {
                // Set process logger for decision audit trail
                if ($this->processLogger) {
                    $this->categoryMappingService->setProcessLogger($this->processLogger);
                }

                // Map provider categories to canonical categories with comprehensive error handling
                $canonicalCategoryIds = $this->categoryMappingService->mapProviderToCanonicalIds($providerCategoryIds, $provider);

                if (empty($canonicalCategoryIds)) {
                    // Track mapping failure for analysis
                    $this->monitoringService->trackFailure(
                        'category_mapping',
                        'No canonical categories found for provider categories',
                        null,
                        $job['id'] ?? null,
                        null,
                        [
                            'provider_category_ids' => $providerCategoryIds,
                            'provider' => $provider,
                            'job_title' => $job['position'] ?? 'unknown'
                        ]
                    );

                    $this->processLogger->logStep('category_mapping', 'warning', 'decision', $provider,
                        'Job excluded due to unmapped categories', [
                        'job_id' => $job['id'] ?? 'unknown',
                        'provider_category_ids' => $providerCategoryIds,
                        'job_title' => $job['position'] ?? 'unknown'
                    ]);

                    Log::warning('JobNotificationService: No canonical categories found for job', [
                        'job_id' => $job['id'] ?? 'unknown',
                        'provider_category_ids' => $providerCategoryIds,
                        'provider' => $provider,
                        'trace_id' => $traceId,
                        'execution_id' => $executionId,
                    ]);
                    $unmappedJobCount++;
                    continue;
                }

                // Replace provider_category_ids with canonical_category_ids
                $mappedJob = $job;
                unset($mappedJob['provider_category_ids']);
                $mappedJob['canonical_category_ids'] = $canonicalCategoryIds;
                
                $mappedJobs[] = $mappedJob;

                Log::debug('JobNotificationService: Job category mapping successful', [
                    'job_id' => $job['id'] ?? 'unknown',
                    'provider_category_ids' => $providerCategoryIds,
                    'canonical_category_ids' => $canonicalCategoryIds,
                    'provider' => $provider,
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);

            } catch (\Throwable $e) {
                // Track category mapping error with full context
                $this->monitoringService->trackFailure(
                    'category_mapping_error',
                    $e->getMessage(),
                    null,
                    $job['id'] ?? null,
                    null,
                    [
                        'provider_category_ids' => $providerCategoryIds,
                        'provider' => $provider,
                        'job_title' => $job['position'] ?? 'unknown',
                        'error_class' => get_class($e)
                    ],
                    $e
                );

                $this->processLogger->logStep('category_mapping', 'error', 'error', $provider,
                    'Category mapping failed with exception', [
                    'job_id' => $job['id'] ?? 'unknown',
                    'provider_category_ids' => $providerCategoryIds,
                    'error_message' => $e->getMessage(),
                    'error_class' => get_class($e)
                ]);

                Log::error('JobNotificationService: Error mapping job categories', [
                    'job_id' => $job['id'] ?? 'unknown',
                    'provider_category_ids' => $providerCategoryIds,
                    'provider' => $provider,
                    'error' => $e->getMessage(),
                    'trace_id' => $traceId,
                    'execution_id' => $executionId,
                ]);
                $unmappedJobCount++;
            }
        }

        if ($unmappedJobCount > 0) {
            Log::warning('JobNotificationService: Some jobs could not be mapped to canonical categories', [
                'unmapped_job_count' => $unmappedJobCount,
                'total_jobs' => count($jobs),
                'mapped_jobs' => count($mappedJobs),
                'provider' => $provider,
                'trace_id' => $traceId,
                'execution_id' => $executionId,
            ]);
        }

        return $mappedJobs;
    }

    /**
     * Extracts a subset of jobs by index range.
     *
     * @param array $jobs Source job array
     * @param int $startIndex Starting index
     * @param int $count Number of jobs to extract
     * @return array Subset of jobs
     */
    private function extractJobsByIndices(array $jobs, int $startIndex, int $count): array
    {
        if ($count <= 0) {
            return [];
        }
        
        return array_slice($jobs, $startIndex, $count);
    }

    /**
     * Trigger missed call alert when jobs are found but no notifications are sent.
     * 
     * This method provides centralized missed call detection for all providers,
     * ensuring consistent behavior and preventing duplicate logic across provider services.
     *
     * @param array $newJobs New jobs that were processed
     * @param array $updatedJobs Updated jobs that were processed  
     * @param array $missedJobs Missed jobs that were processed
     * @param array $scheduleContext Schedule context including provider info
     * @return void
     */
    private function triggerMissedCallAlert(array $newJobs, array $updatedJobs, array $missedJobs, array $scheduleContext): void
    {
        $provider = $scheduleContext['provider'] ?? 'unknown';
        $traceId = $scheduleContext['trace_id'] ?? null;
        $executionId = $scheduleContext['execution_id'] ?? null;
        
        // Build provider-agnostic command name
        $command = $this->buildCommandName($provider);
        
        $context = [
            'command' => $command,
            'provider' => $provider,
            'schedule_rule_id' => $scheduleContext['schedule_rule_id'] ?? null,
            'total_jobs' => count($newJobs) + count($updatedJobs) + count($missedJobs),
            'new_jobs' => count($newJobs),
            'updated_jobs' => count($updatedJobs),
            'missed_jobs' => count($missedJobs),
            'reason' => 'Jobs found but no notifications sent',
            'trace_id' => $traceId,
            'execution_id' => $executionId,
        ];

        Log::warning('JobNotificationService: Triggering missed call alert - jobs found but zero notifications sent', $context);
        
        $this->missedJobService->triggerMissedCallAlert($context);
    }

    /**
     * Build standardized command name based on provider.
     * 
     * @param string $provider Provider name (e.g., 'acbar', 'jobs.af')
     * @return string Standardized command name
     */
    private function buildCommandName(string $provider): string
    {
        $providerLower = strtolower(trim($provider));
        
        switch ($providerLower) {
            case 'acbar':
                return 'jobseeker:sync-acbar-jobs';
            case 'jobs.af':
            case 'jobsaf':
                return 'jobseeker:sync-jobs-af';
            default:
                return "jobseeker:sync-{$providerLower}-jobs";
        }
    }
}
