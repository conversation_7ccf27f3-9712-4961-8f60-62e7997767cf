<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\DB as LaravelDB;

/**
 * Database service wrapper for JobSeeker module
 * Provides centralized database operations and query utilities
 */
final class DB
{
    /**
     * Get database connection for JobSeeker operations
     *
     * @return \Illuminate\Database\Connection
     */
    public static function connection()
    {
        return LaravelDB::connection();
    }

    /**
     * Execute a raw SQL query
     *
     * @param string $query
     * @param array $bindings
     * @return mixed
     */
    public static function select(string $query, array $bindings = [])
    {
        return LaravelDB::select($query, $bindings);
    }

    /**
     * Execute a raw SQL query and return first result
     *
     * @param string $query
     * @param array $bindings
     * @return mixed
     */
    public static function selectOne(string $query, array $bindings = [])
    {
        $results = LaravelDB::select($query, $bindings);
        return $results[0] ?? null;
    }

    /**
     * Execute an insert statement
     *
     * @param string $query
     * @param array $bindings
     * @return bool
     */
    public static function insert(string $query, array $bindings = [])
    {
        return LaravelDB::insert($query, $bindings);
    }

    /**
     * Execute an update statement
     *
     * @param string $query
     * @param array $bindings
     * @return int
     */
    public static function update(string $query, array $bindings = [])
    {
        return LaravelDB::update($query, $bindings);
    }

    /**
     * Execute a delete statement
     *
     * @param string $query
     * @param array $bindings
     * @return int
     */
    public static function delete(string $query, array $bindings = [])
    {
        return LaravelDB::delete($query, $bindings);
    }

    /**
     * Get a query builder instance
     *
     * @param string $table
     * @return \Illuminate\Database\Query\Builder
     */
    public static function table(string $table)
    {
        return LaravelDB::table($table);
    }

    /**
     * Start a database transaction
     *
     * @return void
     */
    public static function beginTransaction(): void
    {
        LaravelDB::beginTransaction();
    }

    /**
     * Commit a database transaction
     *
     * @return void
     */
    public static function commit(): void
    {
        LaravelDB::commit();
    }

    /**
     * Rollback a database transaction
     *
     * @return void
     */
    public static function rollBack(): void
    {
        LaravelDB::rollBack();
    }

    /**
     * Get the current transaction level
     *
     * @return int
     */
    public static function transactionLevel(): int
    {
        return LaravelDB::transactionLevel();
    }

    /**
     * Execute a callback within a database transaction
     *
     * @param callable $callback
     * @return mixed
     */
    public static function transaction(callable $callback)
    {
        return LaravelDB::transaction($callback);
    }

    /**
     * Enable query logging
     *
     * @return void
     */
    public static function enableQueryLog(): void
    {
        LaravelDB::connection()->enableQueryLog();
    }

    /**
     * Get query log
     *
     * @return array
     */
    public static function getQueryLog(): array
    {
        return LaravelDB::connection()->getQueryLog();
    }

    /**
     * Get PDO instance
     *
     * @return \PDO
     */
    public static function getPdo()
    {
        return LaravelDB::connection()->getPdo();
    }
}
