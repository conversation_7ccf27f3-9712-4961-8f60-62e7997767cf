@extends('layouts.hound')
@section('mytitle', 'Mobile Nouraniyah Daily Report')

@section("css")
<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" type="text/css" rel="stylesheet"/>
<link href="https://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<style>
/* Mobile-First Responsive Design */
.mobile-container {
    max-width: 100%;
    margin: 0;
    padding: 10px;
}

/* Header with class info */
.mobile-header {
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.mobile-header h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
}

.mobile-header .date-selector {
    margin-top: 10px;
}

.mobile-header .date-selector input {
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    width: 100%;
    max-width: 200px;
}

/* Performance indicator */
.cache-indicator {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    display: inline-block;
    margin-top: 5px;
}

.cache-indicator.cached {
    background: rgba(40, 199, 111, 0.2);
}

/* Student cards - mobile optimized */
.students-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.student-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.student-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.student-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f1f3f4;
}

.student-name {
    font-weight: 600;
    font-size: 16px;
    color: #1d1d1f;
    margin: 0;
}

.student-id {
    font-size: 12px;
    color: #8e8e93;
    background: #f2f2f7;
    padding: 2px 6px;
    border-radius: 4px;
}

.expand-btn {
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.expand-btn:hover {
    background: #0056CC;
    transform: scale(1.05);
}

.expand-btn:disabled {
    background: #8e8e93;
    cursor: not-allowed;
    transform: none;
}

/* Student form container - hidden by default */
.student-form-container {
    display: none;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f1f3f4;
}

.student-form-container.loading {
    position: relative;
    min-height: 100px;
    background: #f8f9fa;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007AFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form controls optimized for mobile */
.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 120px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6d6d72;
    margin-bottom: 4px;
}

.form-control {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #d1d1d6;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

/* Quick save button */
.quick-save-btn {
    background: linear-gradient(135deg, #34C759 0%, #30B550 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    margin-top: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-save-btn:hover {
    background: linear-gradient(135deg, #30B550 0%, #2CA147 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
}

.quick-save-btn:disabled {
    background: #8e8e93;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Success/Error messages */
.alert {
    padding: 10px 12px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
}

.alert-success {
    background: rgba(52, 199, 89, 0.1);
    color: #30B550;
    border: 1px solid rgba(52, 199, 89, 0.2);
}

.alert-danger {
    background: rgba(255, 59, 48, 0.1);
    color: #FF3B30;
    border: 1px solid rgba(255, 59, 48, 0.2);
}

/* Batch operations */
.batch-actions {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

.batch-actions.show {
    display: block;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.batch-btn {
    background: #007AFF;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    margin-right: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.batch-btn:hover {
    background: #0056CC;
}

/* Responsive breakpoints */
@media (min-width: 768px) {
    .mobile-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .students-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 15px;
    }
    
    .batch-actions {
        position: relative;
        bottom: auto;
        left: auto;
        right: auto;
        margin-top: 20px;
    }
}

@media (min-width: 1024px) {
    .students-container {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}

/* Print styles */
@media print {
    .expand-btn,
    .quick-save-btn,
    .batch-actions {
        display: none !important;
    }
    
    .student-form-container {
        display: block !important;
    }
}
</style>
@endsection

@section('content')
<div class="mobile-container">
    <!-- Header with class information and date selector -->
    <div class="mobile-header">
        <h3>{{ $class->title ?? 'Nouraniyah Class' }}</h3>
        <div class="class-info">
            <small>{{ $class->center->location ?? 'Center' }} | Teacher: {{ $class->assigned_employee->first_name ?? 'Not Assigned' }}</small>
        </div>
        
        <div class="date-selector">
            <input type="date" id="report-date" class="form-control" value="{{ $from_date }}" />
        </div>
        
        @if(isset($isCached) && $isCached)
            <div class="cache-indicator cached">
                <i class="fa fa-lightning"></i> Cached Data - Ultra Fast Load
            </div>
        @else
            <div class="cache-indicator">
                <i class="fa fa-refresh"></i> Fresh Data Loaded
            </div>
        @endif
    </div>

    <!-- Students container -->
    <div class="students-container" id="students-container">
        @if(isset($studentsBasic) && count($studentsBasic) > 0)
            @foreach($studentsBasic as $student)
                <div class="student-card" data-student-id="{{ $student->id }}">
                    <div class="student-header">
                        <div>
                            <h4 class="student-name">{{ $student->full_name }}</h4>
                            <span class="student-id">ID: {{ $student->id }}</span>
                        </div>
                        <button class="expand-btn" onclick="loadStudentForm({{ $student->id }})">
                            <i class="fa fa-plus"></i> Load Form
                        </button>
                    </div>
                    
                    <!-- Student form container - loaded via AJAX -->
                    <div class="student-form-container" id="form-container-{{ $student->id }}">
                        <!-- Form will be loaded here via AJAX -->
                    </div>
                </div>
            @endforeach
        @else
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> No students found for this class and date.
            </div>
        @endif
    </div>

    <!-- Batch actions (shown when forms are loaded) -->
    <div class="batch-actions" id="batch-actions">
        <button class="batch-btn" onclick="saveAllForms()">
            <i class="fa fa-save"></i> Save All Reports
        </button>
        <button class="batch-btn" onclick="expandAllForms()" style="background: #8e8e93;">
            <i class="fa fa-expand"></i> Load All Forms
        </button>
    </div>
</div>
@endsection

@section('js')
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
let loadedForms = new Set();
let classId = {{ $class->id }};
let currentDate = '{{ $from_date }}';

// Initialize date picker
document.addEventListener('DOMContentLoaded', function() {
    flatpickr("#report-date", {
        dateFormat: "Y-m-d",
        defaultDate: currentDate,
        onChange: function(selectedDates, dateStr, instance) {
            if (dateStr !== currentDate) {
                // Reload page with new date
                window.location.href = `{{ route('class.nouranya.mobile.report', $class->id) }}?from_date=${dateStr}`;
            }
        }
    });
});

// Load student form via AJAX
async function loadStudentForm(studentId) {
    const container = document.getElementById(`form-container-${studentId}`);
    const expandBtn = document.querySelector(`[onclick="loadStudentForm(${studentId})"]`);
    
    // Show loading state
    container.style.display = 'block';
    container.classList.add('loading');
    container.innerHTML = '<div class="loading-spinner"></div><p style="margin-top: 10px; color: #8e8e93;">Loading student form...</p>';
    
    expandBtn.disabled = true;
    expandBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Loading...';
    
    try {
        const response = await fetch(`{{ url('workplace/education/class') }}/${classId}/student/${studentId}/form?from_date=${currentDate}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            container.innerHTML = data.html;
            container.classList.remove('loading');
            loadedForms.add(studentId);
            
            expandBtn.innerHTML = '<i class="fa fa-check"></i> Loaded';
            expandBtn.style.background = '#34C759';
            
            // Show batch actions if this is the first form loaded
            if (loadedForms.size === 1) {
                document.getElementById('batch-actions').classList.add('show');
            }
            
            // Show cache indicator if data was cached
            if (data.cached) {
                showToast('Form loaded from cache - Ultra fast!', 'success');
            }
        } else {
            throw new Error(data.message || 'Failed to load form');
        }
    } catch (error) {
        console.error('Error loading student form:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i> 
                Failed to load form: ${error.message}
                <button onclick="loadStudentForm(${studentId})" style="margin-left: 10px; background: none; border: none; color: #007AFF; text-decoration: underline;">
                    Try Again
                </button>
            </div>
        `;
        container.classList.remove('loading');
        
        expandBtn.disabled = false;
        expandBtn.innerHTML = '<i class="fa fa-refresh"></i> Retry';
        expandBtn.style.background = '#FF3B30';
    }
}

// Quick save individual student report
async function quickSaveStudent(studentId) {
    const formData = new FormData();
    const container = document.getElementById(`form-container-${studentId}`);
    const inputs = container.querySelectorAll('input, select, textarea');
    const saveBtn = container.querySelector('.quick-save-btn');
    
    // Collect form data
    const reportData = {};
    inputs.forEach(input => {
        if (input.name) {
            reportData[input.name] = input.value;
        }
    });
    
    formData.append('student_id', studentId);
    formData.append('report_data', JSON.stringify(reportData));
    formData.append('class_time', currentDate);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    
    // Show loading state
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving...';
    
    try {
        const response = await fetch(`{{ url('workplace/education/class') }}/${classId}/report/nouranya/quick-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            saveBtn.innerHTML = '<i class="fa fa-check"></i> Saved!';
            saveBtn.style.background = '#34C759';
            showToast('Report saved successfully!', 'success');
            
            // Reset button after 2 seconds
            setTimeout(() => {
                saveBtn.disabled = false;
                saveBtn.innerHTML = '<i class="fa fa-save"></i> Quick Save';
                saveBtn.style.background = '';
            }, 2000);
        } else {
            throw new Error(data.message || 'Failed to save report');
        }
    } catch (error) {
        console.error('Error saving report:', error);
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fa fa-exclamation-triangle"></i> Error - Retry';
        saveBtn.style.background = '#FF3B30';
        showToast(`Error: ${error.message}`, 'error');
    }
}

// Expand all forms at once
async function expandAllForms() {
    const studentCards = document.querySelectorAll('.student-card');
    const expandAllBtn = document.querySelector('[onclick="expandAllForms()"]');
    
    expandAllBtn.disabled = true;
    expandAllBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Loading All...';
    
    for (const card of studentCards) {
        const studentId = card.dataset.studentId;
        if (!loadedForms.has(parseInt(studentId))) {
            await loadStudentForm(parseInt(studentId));
            // Small delay to prevent overwhelming the server
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    expandAllBtn.disabled = false;
    expandAllBtn.innerHTML = '<i class="fa fa-check"></i> All Loaded';
    expandAllBtn.style.background = '#34C759';
}

// Save all loaded forms
async function saveAllForms() {
    const saveAllBtn = document.querySelector('[onclick="saveAllForms()"]');
    let savedCount = 0;
    let errorCount = 0;
    
    saveAllBtn.disabled = true;
    saveAllBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Saving All...';
    
    for (const studentId of loadedForms) {
        try {
            await quickSaveStudent(studentId);
            savedCount++;
        } catch (error) {
            errorCount++;
        }
        // Small delay between saves
        await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    saveAllBtn.disabled = false;
    saveAllBtn.innerHTML = '<i class="fa fa-save"></i> Save All Reports';
    
    if (errorCount === 0) {
        showToast(`All ${savedCount} reports saved successfully!`, 'success');
    } else {
        showToast(`${savedCount} saved, ${errorCount} failed. Please retry individual forms.`, 'warning');
    }
}

// Toast notification helper
function showToast(message, type = 'info') {
    const Toast = Swal.mixin({
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer)
            toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
    });
    
    Toast.fire({
        icon: type,
        title: message
    });
}

// Handle connection issues gracefully
window.addEventListener('online', function() {
    showToast('Connection restored', 'success');
});

window.addEventListener('offline', function() {
    showToast('Connection lost - some features may not work', 'warning');
});
</script>
@endsection
