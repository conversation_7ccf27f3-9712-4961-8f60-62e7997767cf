<?php

declare(strict_types=1);

namespace Modules\JobSeeker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Mo<PERSON>les\JobSeeker\Repositories\JobRepository;
use <PERSON><PERSON><PERSON>\JobSeeker\Repositories\FilterRepository;
use <PERSON><PERSON><PERSON>\JobSeeker\Entities\JobCategory;
use Mo<PERSON>les\JobSeeker\Entities\ProviderJobCategory;
use Modules\JobSeeker\Services\JobNotificationService;
use Modules\JobSeeker\Services\MissedJobService;
use Modules\JobSeeker\Contracts\JobProviderInterface;

use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use DOMDocument;
use DOMXPath;

use App\Services\EmailService;

/**
 * AcbarJobService orchestrates fetching ACBAR provider jobs, category mapping,
 * and notification delivery. Handles provider-to-canonical category mapping
 * with automatic synchronization to both pivot tables.
 *
 * Purpose: Sync ACBAR jobs with proper category mapping and trigger notifications.
 * Side effects: Creates/updates jobs table, syncs job_provider_category_pivot and
 * job_category_pivot tables, dispatches notifications via JobNotificationService.
 * Security: Rate-limited scraping, input sanitization, SQL injection protection.
 * Errors: Comprehensive logging, graceful degradation on scraping failures,
 * retry mechanisms for transient errors.
 * Dependencies: JobRepository, ProviderJobCategory mappings, JobNotificationService.
 * Performance: Rate limiting, batch processing, optimized DOM parsing.
 */
class AcbarJobService implements \Modules\JobSeeker\Contracts\JobProviderSyncInterface, JobProviderInterface
{
    /**
     * Run-context identifiers for end-to-end log correlation
     */
    protected ?string $traceId = null;
    protected ?int $executionId = null;
    /**
     * @var string
     */
    protected string $baseUrl;

    /**
     * @var int
     */
    protected int $locationId;

    /**
     * @var JobRepository
     */
    protected $jobRepository;

    /**
     * @var FilterRepository
     */
    protected FilterRepository $filterRepository;

    /**
     * @var array
     */
    protected array $categoryCache = [];

    /**
     * @var int Maximum number of retries for rate-limited requests
     */
    protected int $maxRetries;

    /**
     * @var int Base delay in microseconds (1 second)
     */
    protected int $baseDelay;

    /**
     * @var int Timeout in seconds for HTTP requests
     */
    protected int $timeout;

    /**
     * @var SystemErrorNotificationService
     */
    protected SystemErrorNotificationService $errorNotificationService;

    /**
     * @var EmailService
     */
    protected EmailService $emailService;

    /**
     * @var MissedJobService
     */
    protected MissedJobService $missedJobService;

    /**
     * Set runtime context for traceability.
     * 
     * @param string $traceId Unique trace identifier
     * @param int|null $executionId Command execution record ID
     * @param int|null $scheduleRuleId Schedule rule ID if applicable
     * @return void
     */
    public function setRunContext(string $traceId, ?int $executionId = null, ?int $scheduleRuleId = null): void
    {
        $this->traceId = $traceId;
        $this->executionId = $executionId;
    }

    /**
     * Whether to include non-English job titles during this run (per schedule rule)
     */
    private bool $allowNonEnglish = false;

    /**
     * Static cache for canonical categories
     * @deprecated This will be removed after migration to provider categories
     * @var array<int, JobCategory>
     */
    private static $canonicalCategoriesCache = [];

    /**
     * AcbarJobService constructor.
     *
     * @param JobRepository $jobRepository
     * @param FilterRepository $filterRepository
     * @param SystemErrorNotificationService $errorNotificationService
     * @param EmailService $emailService
     * @param MissedJobService $missedJobService
     */
    public function __construct(
        JobRepository $jobRepository,
        FilterRepository $filterRepository,
        SystemErrorNotificationService $errorNotificationService,
        EmailService $emailService,
        MissedJobService $missedJobService
    ) {
        $this->jobRepository = $jobRepository;
        $this->filterRepository = $filterRepository;
        $this->errorNotificationService = $errorNotificationService;
        $this->emailService = $emailService;
        $this->missedJobService = $missedJobService;

        // Load configuration from config/jobseeker.php
        $config = config('jobseeker.acbar_default_filters', []);

        $this->baseUrl = $config['base_url'] ?? 'https://www.acbar.org/jobs';
        $this->locationId = $config['default_location_id'] ?? 14;
        $this->timeout = $config['timeout'] ?? 60;
        $this->maxRetries = $config['max_retries'] ?? 5;
        $this->baseDelay = $config['base_delay'] ?? 1000000;

        // Default from config; will be overridden per schedule rule when available
        $this->allowNonEnglish = (bool) ($config['allow_non_english'] ?? false);
    }

    /**
     * Get provider categories from schedule rule for filtering
     * This is the new correct approach using provider categories
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderCategoriesFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            // No schedule rule - return all ACBAR categories
            return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::find($scheduleRuleId);
            if (!$rule || empty($rule->provider_category_ids)) {
                Log::info('AcbarJobService: No category filter in schedule rule', [
                    'schedule_rule_id' => $scheduleRuleId
                ]);
                // Return all ACBAR categories
                return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                    ->pluck('provider_identifier')
                    ->toArray();
            }

            $providerCategories = \Modules\JobSeeker\Entities\ProviderJobCategory::whereIn('id', $rule->provider_category_ids)
                ->where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();

            Log::info('AcbarJobService: Retrieved provider categories from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'provider_category_ids' => $rule->provider_category_ids,
                'provider_identifiers' => $providerCategories
            ]);

            return $providerCategories;

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error getting provider categories from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            // Fallback to all categories
            return \Modules\JobSeeker\Entities\ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();
        }
    }

    /**
     * Get provider locations from schedule rule for filtering
     *
     * @param int|null $scheduleRuleId
     * @return array Array of provider_identifier values for API calls
     */
    protected function getProviderLocationsFromScheduleRule(?int $scheduleRuleId): array
    {
        if (!$scheduleRuleId) {
            return [];
        }

        try {
            $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
            if (!$rule || !$rule->filter || empty($rule->filter->locations)) {
                return [];
            }

            return \Modules\JobSeeker\Entities\ProviderJobLocation::whereIn('id', $rule->filter->locations)
                ->where('provider_name', 'acbar')
                ->where('is_active', true)
                ->pluck('provider_identifier')
                ->toArray();

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error getting provider locations from schedule rule', [
                'schedule_rule_id' => $scheduleRuleId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Make a rate-limited HTTP request with retries and exponential backoff
     *
     * @param string $url
     * @return \Illuminate\Http\Client\Response
     * @throws \Exception
     */
    protected function makeRateLimitedRequest(string $url)
    {
        $rateLimitKey = 'acbar_api_request';
        $maxAttempts = $this->maxRetries;
        $baseDelay = $this->baseDelay; // 1 second in microseconds

        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                if (RateLimiter::tooManyAttempts($rateLimitKey, 10)) {
                    $retryAfter = RateLimiter::availableIn($rateLimitKey);
                    Log::warning("Rate limited - waiting {$retryAfter} seconds", [
                        'attempt' => $attempt,
                        'url' => $url
                    ]);
                    sleep($retryAfter);
                }

                RateLimiter::hit($rateLimitKey, 60);

                $response = Http::timeout($this->timeout)
                    ->withUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
                    ->get($url);

                if ($response->successful()) {
                    Log::debug("HTTP request successful", [
                        'url' => $url,
                        'attempt' => $attempt,
                        'status' => $response->status()
                    ]);
                    return $response;
                }

                Log::warning("HTTP request failed", [
                    'url' => $url,
                    'attempt' => $attempt,
                    'status' => $response->status(),
                    'body' => substr($response->body(), 0, 200)
                ]);

                if ($attempt < $maxAttempts) {
                    $delayMs = $baseDelay * (2 ** ($attempt - 1));
                    usleep($delayMs);
                }

            } catch (\Exception $e) {
                Log::error("Exception during HTTP request", [
                    'url' => $url,
                    'attempt' => $attempt,
                    'error' => $e->getMessage()
                ]);

                if ($attempt < $maxAttempts) {
                    $delayMs = $baseDelay * (2 ** ($attempt - 1));
                    usleep($delayMs);
                } else {
                    throw $e;
                }
            }
        }

        throw new \Exception("Failed to make HTTP request after {$maxAttempts} attempts");
    }

    /**
     * Basic English title detection (reuse JobsAfService semantics)
     */
    protected function isEnglishTitle(string $text): bool
    {
        $detector = app(\Modules\JobSeeker\Services\LanguageDetectionService::class);
        return $detector->isEnglishTitle($text, 'acbar');
    }

    /**
     * Make a standard HTTP request
     *
     * @param string $url
     * @return \Illuminate\Http\Client\Response
     */
    protected function makeRequest(string $url)
    {
        return $this->makeRateLimitedRequest($url);
    }



    /**
     * Sync jobs for a specific ACBAR category and location
     *
     * @param string $categoryId The ACBAR provider identifier to sync
     * @param string|null $locationId The ACBAR location identifier (defaults to configured default)
     * @return array Statistics about the sync process for this category
     */
    public function syncAcbarCategory(string $categoryId, ?string $locationId = null): array
    {
        $stats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'category_processed' => false
        ];

        try {
            // Check if this provider identifier exists in our mappings
            $providerCategory = ProviderJobCategory::where('provider_name', 'acbar')
                ->where('provider_identifier', $categoryId)
                ->first();

            if (!$providerCategory) {
                Log::info("Skipping ACBAR category - no provider mapping found", [
                    'provider_identifier' => $categoryId
                ]);
                $stats['skipped_no_category_map']++;
                return $stats;
            }

            // FIXED: Use dynamic location ID or fall back to configured default
            $effectiveLocationId = $locationId ?? $this->locationId;
            $url = "{$this->baseUrl}?location={$effectiveLocationId}&category={$categoryId}";
            Log::info("Fetching jobs from ACBAR.org", [
                'url' => $url,
                'provider_identifier' => $categoryId,
                'provider_category_name' => $providerCategory->name,
                'location_id' => $effectiveLocationId,
                'location_source' => $locationId ? 'dynamic' : 'default_config'
            ]);

            // Use rate-limited request
            $response = $this->makeRequest($url);
            Log::debug("ACBAR response received", [
                'status' => $response->status(),
                'body_length' => strlen($response->body()),
                'url' => $url,
                'has_content' => !empty($response->body())
            ]);

            // Parse HTML
            $document = new DOMDocument();
            @$document->loadHTML($response->body());
            $xpath = new DOMXPath($document);

            // Find the job table
            $jobTable = $xpath->query('//table[@class="table table-bordered"]');
            Log::info("Job table search result", [
                'table_found' => $jobTable->length > 0,
                'provider_identifier' => $categoryId,
                'table_count' => $jobTable->length
            ]);

            if ($jobTable->length === 0) {
                Log::warning("No job table found for category", [
                    'provider_identifier' => $categoryId,
                    'html_snippet' => substr($response->body(), 0, 500)
                ]);
                return $stats;
            }

            // Get the category name from the HTML or use provider category name
            $categoryName = $providerCategory->name;
            $select = $xpath->query('//select[@id="data_industry"]');

            if ($select->length > 0) {
                $selectedOption = $xpath->query('.//option[@selected]', $select->item(0));

                if (!$selectedOption || $selectedOption->length === 0) {
                    $selectedOption = $xpath->query(".//option[@value='{$categoryId}']", $select->item(0));
                }

                if ($selectedOption && $selectedOption->length > 0) {
                    $categoryName = trim($selectedOption->item(0)->nodeValue);
                }
            }

            Log::info("Category name determined", [
                'found_name' => $categoryName,
                'provider_identifier' => $categoryId,
                'provider_category_name' => $providerCategory->name
            ]);

            // Save/retrieve the ACBAR category
            $acbarCategory = $this->saveAcbarCategory($categoryId, $categoryName);
            if (!$acbarCategory) {
                $stats['skipped_no_category_map']++;
                Log::warning("Failed to save/retrieve ACBAR category", [
                    'provider_identifier' => $categoryId,
                    'category_name' => $categoryName
                ]);
                return $stats;
            }

            Log::info("ACBAR category retrieved/saved", [
                'acbar_category_id' => $acbarCategory->id,
                'acbar_category_name' => $acbarCategory->name,
                'parent_id' => $acbarCategory->parent_id,
                'source' => $acbarCategory->source,
                'source_id' => $acbarCategory->source_id
            ]);

            // Get the canonical category through the parent relationship
            $canonicalCategory = $acbarCategory->parent;
            if (!$canonicalCategory) {
                Log::error("ACBAR category has no parent (canonical) category", [
                    'acbar_category_id' => $acbarCategory->id,
                    'acbar_category_name' => $acbarCategory->name,
                    'provider_identifier' => $categoryId
                ]);
                $stats['errors']++;
                return $stats;
            }

            Log::info("Canonical category found", [
                'canonical_id' => $canonicalCategory->id,
                'canonical_name' => $canonicalCategory->name,
                'is_canonical' => $canonicalCategory->is_canonical,
                'is_active' => $canonicalCategory->is_active
            ]);

            $stats['category_processed'] = true;

            // Process job listings - get all rows except header
            $rows = $xpath->query('//table[@class="table table-bordered"]/tr[position()>1]');
            Log::info("Found job rows", [
                'count' => $rows->length,
                'provider_identifier' => $categoryId,
                'has_rows' => $rows->length > 0
            ]);

            foreach ($rows as $row) {
                try {
                    $cells = $xpath->query('td', $row);
                    if ($cells->length < 5) {
                        Log::warning("Invalid row structure", [
                            'provider_identifier' => $categoryId,
                            'cells_count' => $cells->length,
                            'expected_cells' => 5,
                            'row_html' => $document->saveHTML($row)
                        ]);
                        continue;
                    }

                    // Extract job data
                    $jobTitle = trim($cells->item(1)->nodeValue);
                    $jobUrl = trim($cells->item(1)->getElementsByTagName('a')->item(0)->getAttribute('href'));
                    if (Str::startsWith($jobUrl, '/')) {
                        $jobUrl = 'https://www.acbar.org' . $jobUrl;
                    }

                    $organizationName = trim($cells->item(2)->nodeValue);

                    // Extract location
                    $locationLinks = $xpath->query('a', $cells->item(3));
                    $locations = [];
                    foreach ($locationLinks as $link) {
                        if (Str::contains($link->getAttribute('href'), '/job/location/')) {
                            $locations[] = trim($link->nodeValue);
                        }
                    }
                    $locationString = implode(', ', $locations);

                    // Parse close date
                    $closeDateString = trim($cells->item(4)->nodeValue);
                    $expireDate = Carbon::parse($closeDateString)->format('Y-m-d');

                    Log::info("Extracted job data", [
                        'title' => $jobTitle,
                        'organization' => $organizationName,
                        'location' => $locationString,
                        'expire_date' => $expireDate,
                        'url' => $jobUrl,
                        'provider_identifier' => $categoryId
                    ]);

                        // Prepare job data
                    $jobData = [
                        'position' => $jobTitle,
                        'company_name' => $organizationName,
                        'slug' => Str::slug($organizationName . '-' . $jobTitle . '-' . md5($jobUrl)),
                        'description' => 'Job details available at: ' . $jobUrl,
                        'publish_date' => Carbon::now()->format('Y-m-d'),
                        'expire_date' => $expireDate,
                        'locations' => $locationString,
                        'source' => 'ACBAR',
                        'url' => $jobUrl,
                        'is_active' => true,
                        'organizationName' => $organizationName,
                        'jobTitle' => $jobTitle,
                        'location' => $locationString,
                        'jobUrl' => $jobUrl,
                        // Add normalized fields for deduplication
                        'normalized_company_name' => $organizationName,
                        'normalized_job_title' => $jobTitle,
                        'normalized_location' => $locationString,
                        'job_fingerprint' => md5(strtolower($organizationName . $jobTitle . $locationString))
                    ];

                    DB::beginTransaction();
                    try {
                        // Guard empty or too-short titles
                        $trimmedTitle = trim($jobTitle);
                        if ($trimmedTitle === '' || mb_strlen($trimmedTitle) < 2) {
                            $stats['empty_title_skipped'] = ($stats['empty_title_skipped'] ?? 0) + 1;
                            DB::rollBack();
                            continue;
                        }

                        // Respect per-rule language setting: skip/include non-English based on flag
                        $isEnglish = $this->isEnglishTitle($trimmedTitle);
                        if (!$this->allowNonEnglish && !$isEnglish) {
                            $stats['non_english_skipped'] = ($stats['non_english_skipped'] ?? 0) + 1;
                            DB::rollBack();
                            continue;
                        } elseif ($this->allowNonEnglish && !$isEnglish) {
                            $stats['non_english_included'] = ($stats['non_english_included'] ?? 0) + 1;
                        }

                        // Create or update job
                        $job = $this->jobRepository->createOrUpdate($jobData);
                        $jobWasCreated = $job->wasRecentlyCreated;

                        // NEW APPROACH: Attach provider categories
                        $job->providerCategories()->sync([$providerCategory->id]);
                        Log::info('AcbarJobService: Synced job with provider categories', [
                            'job_id' => $job->id,
                            'provider_category_id' => $providerCategory->id,
                            'provider_category_name' => $providerCategory->name
                        ]);

                        // CRITICAL: Also sync canonical categories for notification system compatibility
                        if ($providerCategory->canonical_category_id) {
                            $job->categories()->syncWithoutDetaching([$providerCategory->canonical_category_id]);
                            Log::info('AcbarJobService: Synced job with canonical categories', [
                                'job_id' => $job->id,
                                'canonical_category_id' => $providerCategory->canonical_category_id,
                                'canonical_category_name' => $providerCategory->canonicalCategory?->name
                            ]);
                        }

                        DB::commit();

                        // Aggregate for centralized notification hub (Jobs.af methodology)
                        // Build minimal job array compatible with hub expectations
                        if (!isset($this->aggregatedNewJobs)) { $this->aggregatedNewJobs = []; }
                        if (!isset($this->aggregatedUpdatedJobs)) { $this->aggregatedUpdatedJobs = []; }

                        // Get provider category IDs for JobNotificationService mapping (CRITICAL FIX)
                        $providerCategoryIds = DB::table('job_provider_category_pivot')
                            ->where('job_id', $job->id)
                            ->pluck('provider_category_id')
                            ->toArray();

                        // DEBUG: Log the provider category IDs being used for notifications
                        Log::debug('AcbarJobService: Provider category IDs for notification mapping', [
                            'job_id' => $job->id,
                            'position' => $job->position,
                            'provider_category_ids' => $providerCategoryIds,
                            'trace_id' => $this->traceId,
                            'execution_id' => $this->executionId,
                        ]);

                        $jobPayload = [
                            'id' => $job->id,
                            'position' => $job->position,
                            'company_name' => $job->company_name,
                            'locations' => $job->locations,
                            'publish_date' => $job->publish_date,
                            'provider_category_ids' => $providerCategoryIds,  // Required by JobNotificationService
                        ];

                        if ($jobWasCreated) {
                            $this->aggregatedNewJobs[] = $jobPayload;
                        } else {
                            $this->aggregatedUpdatedJobs[] = $jobPayload;
                        }

                        if ($jobWasCreated) {
                            $stats['created']++;
                            Log::info("ACBAR: Created new job", [
                                'job_id' => $job->id,
                                'title' => $jobTitle,
                                'organization' => $organizationName,
                                'provider_identifier' => $categoryId
                            ]);
                        } else {
                            $stats['updated']++;
                            Log::info("ACBAR: Updated existing job", [
                                'job_id' => $job->id,
                                'title' => $jobTitle,
                                'organization' => $organizationName,
                                'provider_identifier' => $categoryId
                            ]);
                        }
                    } catch (\Exception $e) {
                        DB::rollBack();
                        throw $e;
                    }
                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error("Error processing job row", [
                        'provider_identifier' => $categoryId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
        } catch (\Exception $e) {
            $stats['errors']++;
            Log::error("Error processing ACBAR category", [
                'provider_identifier' => $categoryId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $stats;
    }

    /**
     * Sync jobs from ACBAR.org using provider identifiers directly
     * This method now accepts ACBAR provider identifiers directly from provider_job_categories table
     *
     * @param array|null $providerIdentifiers Array of ACBAR provider identifiers (e.g., ["16", "70"])
     * @param int|null $scheduleRuleId Optional schedule rule ID for translated filters
     * @return array Statistics about the sync process
     */
    public function syncAcbarJobs($providerIdentifiers = null, ?int $scheduleRuleId = null): array
    {
        // Reset category cache
        $this->categoryCache = [];

        $startTime = microtime(true);
        $totalStats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'categories_processed' => 0,
            'filtered_categories' => [],
            // Health dashboard fields
            'jobs_fetched' => 0,
            'jobs_by_category' => [],
            'error_types' => [],
            'api_response_time' => 0
        ];

        // Sanitize input: support string, array, or null
        if (is_string($providerIdentifiers)) {
            $providerIdentifiers = [$providerIdentifiers];
        } elseif (!is_array($providerIdentifiers)) {
            $providerIdentifiers = [];
        }

        Log::info('Starting ACBAR.org job synchronization', [
            'provider_identifiers' => !empty($providerIdentifiers) ? $providerIdentifiers : 'all/filtered',
            'schedule_rule_id' => $scheduleRuleId
        ]);

        try {
            // Priority 1: Manual provider identifier filtering takes precedence
            if (!empty($providerIdentifiers)) {
                // Precedence for manual run (no scheduleRuleId): provider default > global
                if ($scheduleRuleId === null) {
                    $providerSetting = \Modules\JobSeeker\Entities\ProviderSetting::forProvider('acbar');
                    if ($providerSetting) {
                        $this->allowNonEnglish = (bool) $providerSetting->allow_non_english_default;
                        Log::info('AcbarJobService: Resolved allow_non_english for manual run', [
                            'provider' => 'acbar',
                            'source' => 'provider',
                            'value' => $this->allowNonEnglish,
                        ]);
                    } else {
                        $this->allowNonEnglish = (bool) (config('jobseeker.acbar_default_filters.allow_non_english') ?? false);
                        Log::info('AcbarJobService: Resolved allow_non_english for manual run', [
                            'provider' => 'acbar',
                            'source' => 'global',
                            'value' => $this->allowNonEnglish,
                        ]);
                    }
                }
                // Convert to strings and validate they exist in provider_job_categories
                $validIdentifiers = [];
                foreach ($providerIdentifiers as $identifier) {
                    $identifier = (string)$identifier;
                    $exists = ProviderJobCategory::where('provider_name', 'acbar')
                        ->where('provider_identifier', $identifier)
                        ->exists();

                    if ($exists) {
                        $validIdentifiers[] = $identifier;
                    } else {
                        Log::warning("Invalid ACBAR provider identifier", [
                            'provider_identifier' => $identifier
                        ]);
                    }
                }

                $totalStats['filtered_categories'] = $validIdentifiers;

                Log::info('Using manual provider identifier filtering for ACBAR sync', [
                    'requested_identifiers' => $providerIdentifiers,
                    'valid_identifiers' => $validIdentifiers,
                    'identifier_count' => count($validIdentifiers)
                ]);

                // Early sanity check: if list is empty after validation, return immediately
                if (empty($validIdentifiers)) {
                    Log::warning('ACBAR sync aborted: no valid provider identifiers found', [
                        'requested_identifiers' => $providerIdentifiers,
                        'message' => 'The provided provider identifiers do not exist in provider_job_categories table'
                    ]);
                    $totalStats['api_response_time'] = microtime(true) - $startTime;
                    $totalStats['skipped_no_category_map'] = count($providerIdentifiers);
                    return $this->formatHealthTrackingReturn($totalStats, true, 'No valid ACBAR provider identifiers found');
                }

                // Process manual provider identifiers sequentially
                $totalCategories = count($validIdentifiers);
                foreach ($validIdentifiers as $index => $identifier) {
                    $categoryStartTime = microtime(true);
                    $currentCategory = $index + 1;

                    Log::info("ACBAR Manual Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                        'provider_identifier' => $identifier,
                        'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                    ]);

                    // Process single category with full backend completion
                    $stats = $this->processAcbarCategoryCompletely($identifier, $scheduleRuleId);
                    $this->aggregateHealthStats($stats, $totalStats);

                    $categoryDuration = microtime(true) - $categoryStartTime;
                    Log::info("ACBAR Manual Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                        'provider_identifier' => $identifier,
                        'jobs_processed' => $stats['jobs_fetched'],
                        'duration_seconds' => round($categoryDuration, 2),
                        'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                    ]);

                    // Enhanced delay based on jobs processed
                    if ($currentCategory < $totalCategories) {
                        $this->smartDelayBetweenCategories($stats, $categoryDuration);
                    }
                }

                // Calculate total API response time
                $totalStats['api_response_time'] = microtime(true) - $startTime;

                return $this->formatHealthTrackingReturn($totalStats, true);
            }

            // Priority 2: If a schedule rule ID is provided, use provider categories from CommandScheduleFilter.categories via repository
            if ($scheduleRuleId !== null) {
                try {
                    // Unified approach: translate CommandScheduleFilter.categories to provider identifiers via repository
                    $translatedFilters = $this->filterRepository->getAcbarTranslatedFilters($scheduleRuleId);
                    $providerIdentifiers = $translatedFilters['category_ids'] ?? [];

                    // Ensure per-rule language flag is honored
                    try {
                        $rule = \Modules\JobSeeker\Entities\CommandScheduleRule::with('filter')->find($scheduleRuleId);
                        if ($rule && $rule->filter) {
                            $this->allowNonEnglish = (bool) ($rule->filter->allow_non_english ?? $this->allowNonEnglish);
                        } else {
                            // Fallback to provider default > global when rule has no filter
                            $providerSetting = \Modules\JobSeeker\Entities\ProviderSetting::forProvider('acbar');
                            if ($providerSetting) {
                                $this->allowNonEnglish = (bool) $providerSetting->allow_non_english_default;
                                Log::info('AcbarJobService: Resolved allow_non_english (no rule filter)', [
                                    'provider' => 'acbar',
                                    'schedule_rule_id' => $scheduleRuleId,
                                    'source' => 'provider',
                                    'value' => $this->allowNonEnglish,
                                ]);
                            } else {
                                $this->allowNonEnglish = (bool) (config('jobseeker.acbar_default_filters.allow_non_english') ?? false);
                                Log::info('AcbarJobService: Resolved allow_non_english (no rule filter)', [
                                    'provider' => 'acbar',
                                    'schedule_rule_id' => $scheduleRuleId,
                                    'source' => 'global',
                                    'value' => $this->allowNonEnglish,
                                ]);
                            }
                        }
                    } catch (\Throwable $t) {
                        // Keep default if anything goes wrong
                    }

                    Log::info('Using unified provider category approach for ACBAR sync', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'provider_identifiers' => $providerIdentifiers,
                        'identifier_count' => count($providerIdentifiers)
                    ]);

                    // Apply per-rule language inclusion and advanced settings from repository
                    $this->allowNonEnglish = (bool) ($translatedFilters['allow_non_english'] ?? $this->allowNonEnglish);
                    if (isset($translatedFilters['max_retries'])) { $this->maxRetries = $translatedFilters['max_retries']; }
                    if (isset($translatedFilters['timeout'])) { $this->timeout = $translatedFilters['timeout']; }
                    if (isset($translatedFilters['base_delay'])) { $this->baseDelay = $translatedFilters['base_delay']; }

                    Log::info('Using translated ACBAR filters from schedule rule', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'provider_identifiers' => $providerIdentifiers,
                        'location_ids' => $translatedFilters['location_ids'] ?? [],
                        'allow_non_english' => $this->allowNonEnglish,
                        'max_retries' => $this->maxRetries,
                        'timeout' => $this->timeout,
                        'base_delay' => $this->baseDelay
                    ]);

                    if (empty($providerIdentifiers)) {
                        // No identifier filter = sync all available ACBAR categories
                        Log::info('No identifier filter specified, syncing all ACBAR categories');
                        $providerIdentifiers = ProviderJobCategory::where('provider_name', 'acbar')
                            ->pluck('provider_identifier')
                            ->toArray();
                    }

                    $totalStats['filtered_categories'] = $providerIdentifiers;

                    // Process filtered provider identifiers sequentially
                    $totalCategories = count($providerIdentifiers);
                    foreach ($providerIdentifiers as $index => $identifier) {
                        $categoryStartTime = microtime(true);
                        $currentCategory = $index + 1;

                        Log::info("ACBAR Sequential Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                            'provider_identifier' => $identifier,
                            'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                        ]);

                        // Process single category with full backend completion
                        $stats = $this->processAcbarCategoryCompletely((string) $identifier, $scheduleRuleId);
                        $this->aggregateHealthStats($stats, $totalStats);

                        $categoryDuration = microtime(true) - $categoryStartTime;
                        Log::info("ACBAR Sequential Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                            'provider_identifier' => $identifier,
                            'jobs_processed' => $stats['jobs_fetched'],
                            'duration_seconds' => round($categoryDuration, 2),
                            'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                        ]);

                        // Enhanced delay based on jobs processed
                        if ($currentCategory < $totalCategories) {
                            $this->smartDelayBetweenCategories($stats, $categoryDuration);
                        }
                    }

                    // Calculate total API response time
                    $totalStats['api_response_time'] = microtime(true) - $startTime;

                    return $this->formatHealthTrackingReturn($totalStats, true);

                } catch (\Exception $e) {
                    Log::error('Failed to get translated filters for ACBAR', [
                        'schedule_rule_id' => $scheduleRuleId,
                        'error' => $e->getMessage()
                    ]);
                    // Fall through to default behavior
                }
            }

            // Priority 3: Default behavior - sync all available ACBAR categories
            Log::info('Using default behavior: syncing all ACBAR categories');

            $allProviderIdentifiers = ProviderJobCategory::where('provider_name', 'acbar')
                ->pluck('provider_identifier')
                ->toArray();

            $totalStats['filtered_categories'] = $allProviderIdentifiers;
            $totalCategories = count($allProviderIdentifiers);
            $currentCategory = 0;

            foreach ($allProviderIdentifiers as $identifier) {
                $categoryStartTime = microtime(true);
                $currentCategory++;

                Log::info("ACBAR Default Processing: Starting provider identifier {$currentCategory}/{$totalCategories}", [
                    'provider_identifier' => $identifier,
                    'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                ]);

                // Process single category with full backend completion
                $stats = $this->processAcbarCategoryCompletely($identifier, $scheduleRuleId);
                $this->aggregateHealthStats($stats, $totalStats);

                $categoryDuration = microtime(true) - $categoryStartTime;
                Log::info("ACBAR Default Processing: Provider identifier {$currentCategory}/{$totalCategories} completed", [
                    'provider_identifier' => $identifier,
                    'jobs_processed' => $stats['jobs_fetched'],
                    'duration_seconds' => round($categoryDuration, 2),
                    'progress' => round(($currentCategory / $totalCategories) * 100, 1) . '%'
                ]);

                // Enhanced delay based on jobs processed
                if ($currentCategory < $totalCategories) {
                    $this->smartDelayBetweenCategories($stats, $categoryDuration);
                }
            }

            // Calculate total API response time
            $totalStats['api_response_time'] = microtime(true) - $startTime;

            return $this->formatHealthTrackingReturn($totalStats, true);

        } catch (\Exception $e) {
            $totalStats['errors']++;
            $totalStats['api_response_time'] = microtime(true) - $startTime;

            Log::error('Critical error in ACBAR job synchronization', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Report critical error to founder
            $this->errorNotificationService->reportJobFetchFailure(
                'ACBAR',
                'Critical error in ACBAR job synchronization: ' . $e->getMessage(),
                [
                    'total_stats' => $totalStats,
                    'api_response_time' => $totalStats['api_response_time'],
                    'schedule_rule_id' => $scheduleRuleId,
                    'category_ids' => $providerIdentifiers ?? [],
                ],
                $e
            );

            return $this->formatHealthTrackingReturn($totalStats, false, $e->getMessage());
        }
    }

    /**
     * Aggregate health statistics from individual category sync operations
     * 
     * @param array $stats
     * @param array $totalStats
     * @return void
     */
    private function aggregateHealthStats(array $stats, array &$totalStats): void
    {
        $totalStats['created'] += $stats['created'];
        $totalStats['updated'] += $stats['updated'];
        $totalStats['errors'] += $stats['errors'];
        $totalStats['skipped_no_category_map'] += $stats['skipped_no_category_map'];
        if ($stats['category_processed']) {
            $totalStats['categories_processed']++;
        }

        // Aggregate health dashboard fields
        $totalStats['jobs_fetched'] += $stats['jobs_fetched'] ?? 0;

        // Aggregate language metrics if present
        if (!empty($stats['non_english_skipped'])) {
            $totalStats['non_english_skipped'] = ($totalStats['non_english_skipped'] ?? 0) + $stats['non_english_skipped'];
        }
        if (!empty($stats['non_english_included'])) {
            $totalStats['non_english_included'] = ($totalStats['non_english_included'] ?? 0) + $stats['non_english_included'];
        }
        if (!empty($stats['empty_title_skipped'])) {
            $totalStats['empty_title_skipped'] = ($totalStats['empty_title_skipped'] ?? 0) + $stats['empty_title_skipped'];
        }

        // Merge jobs by category
        if (!empty($stats['jobs_by_category'])) {
            foreach ($stats['jobs_by_category'] as $categoryName => $count) {
                if (!isset($totalStats['jobs_by_category'][$categoryName])) {
                    $totalStats['jobs_by_category'][$categoryName] = 0;
                }
                $totalStats['jobs_by_category'][$categoryName] += $count;
            }
        }

        // Merge error types
        if (!empty($stats['error_types'])) {
            foreach ($stats['error_types'] as $errorType => $count) {
                if (!isset($totalStats['error_types'][$errorType])) {
                    $totalStats['error_types'][$errorType] = 0;
                }
                $totalStats['error_types'][$errorType] += $count;
            }
        }
    }

    /**
     * Format the return array with health tracking fields
     * 
     * @param array $stats
     * @param bool $success
     * @param string|null $errorMessage
     * @return array
     */
    private function formatHealthTrackingReturn(array $stats, bool $success, ?string $errorMessage = null): array
    {
        $result = [
            'success' => $success,
            'created' => $stats['created'],
            'updated' => $stats['updated'],
            'errors' => $stats['errors'],
            'skipped_no_category_map' => $stats['skipped_no_category_map'],
            'categories_processed' => $stats['categories_processed'],
            'filtered_categories' => $stats['filtered_categories'],
            // Health dashboard fields
            'jobs_fetched' => $stats['jobs_fetched'],
            'jobs_by_category' => $stats['jobs_by_category'],
            'error_types' => $stats['error_types'],
            'api_response_time' => $stats['api_response_time']
        ];

        // Include language metrics when available
        foreach (['non_english_skipped', 'non_english_included', 'empty_title_skipped'] as $k) {
            if (isset($stats[$k])) {
                $result[$k] = $stats[$k];
            }
        }

        if (!$success && $errorMessage) {
            $result['error_message'] = $errorMessage;
        }

        return $result;
    }

    /**
     * Classify error type for health dashboard tracking
     * 
     * @param \Exception $exception
     * @return string
     */
    private function classifyError(\Exception $exception): string
    {
        $message = strtolower($exception->getMessage());
        $exceptionClass = get_class($exception);

        // Standardized taxonomy
        if (str_contains($message, 'rate limit') || str_contains($message, 'too many requests') || str_contains($message, '429')) return 'RATE_LIMIT';
        if (str_contains($message, 'timeout') || str_contains($message, 'time out') || str_contains($exceptionClass, 'Timeout')) return 'TIMEOUT';
        if (str_contains($message, 'connection') || str_contains($message, 'network') || str_contains($message, 'curl') || str_contains($exceptionClass, 'Connection')) return 'NETWORK';
        if (str_contains($message, 'api') || str_contains($message, 'json') || str_contains($message, 'response') || str_contains($message, 'http') || str_contains($exceptionClass, 'Request') || str_contains($exceptionClass, 'Response')) return 'API';
        if (str_contains($message, 'database') || str_contains($message, 'sql') || str_contains($message, 'duplicate') || str_contains($message, 'constraint') || str_contains($exceptionClass, 'Query') || str_contains($exceptionClass, 'Database')) return 'DATA';
        return 'UNKNOWN';
    }



    /**
     * Save/retrieve ACBAR category in the job_categories table
     *
     * @param string $categoryId ACBAR provider identifier
     * @param string $categoryName ACBAR category name
     * @return JobCategory|null
     */
    private function saveAcbarCategory(string $categoryId, string $categoryName): ?JobCategory
    {
        try {
            // First, try to find existing category by source and source_id
            $category = JobCategory::findBySource('acbar', $categoryId);

            if ($category) {
                Log::debug('AcbarJobService: Found existing ACBAR category', [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'source_id' => $categoryId
                ]);
                return $category;
            }

            // Get canonical category mapping from config
            $categoryMapping = config('jobseeker.acbar_default_filters.category_mapping', []);
            $canonicalCategoryId = $categoryMapping[$categoryId] ?? null;

            // Create new category
            $category = new JobCategory();
            $category->name = $categoryName;
            $category->slug = Str::slug($categoryName . '-acbar-' . $categoryId); // Generate unique slug
            $category->source = 'acbar';
            $category->source_id = $categoryId;
            $category->is_active = true;
            $category->is_canonical = false; // ACBAR categories are not canonical
            $category->is_archived = false;

            // Set parent_id if we have a canonical mapping
            if ($canonicalCategoryId) {
                $canonicalCategory = JobCategory::find($canonicalCategoryId);
                if ($canonicalCategory) {
                    $category->parent_id = $canonicalCategory->id;
                    Log::debug('AcbarJobService: Mapped ACBAR category to canonical', [
                        'acbar_category_id' => $categoryId,
                        'canonical_category_id' => $canonicalCategoryId,
                        'canonical_category_name' => $canonicalCategory->name
                    ]);
                }
            }

            $category->save();

            Log::info('AcbarJobService: Created new ACBAR category', [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'source_id' => $categoryId,
                'parent_id' => $category->parent_id
            ]);

            return $category;

        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error saving ACBAR category', [
                'category_id' => $categoryId,
                'category_name' => $categoryName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get ACBAR category name by provider identifier
     *
     * @param string $providerIdentifier
     * @return string|null
     */
    private function getAcbarCategoryName(string $providerIdentifier): ?string
    {
        try {
            // Get provider category mapping
            $providerCategory = ProviderJobCategory::where('provider_name', 'acbar')
                ->where('provider_identifier', $providerIdentifier)
                ->first();

            if ($providerCategory) {
                return $providerCategory->name;
            }

            // Fallback: Use generic ACBAR category naming
            return "ACBAR Category {$providerIdentifier}";

        } catch (\Exception $e) {
            Log::warning('AcbarJobService: Error getting category name', [
                'provider_identifier' => $providerIdentifier,
                'error' => $e->getMessage()
            ]);
            return "Unknown Category {$providerIdentifier}";
        }
    }

    /**
     * Format execution stats for command schedule health tracking
     * 
     * @param array $stats
     * @return array
     */
    public function formatExecutionStats(array $stats): array
    {
        try {
            return [
                'jobs_fetched' => (int) ($stats['jobs_fetched'] ?? (($stats['created'] ?? 0) + ($stats['updated'] ?? 0))),
                'jobs_by_category' => $stats['jobs_by_category'] ?? [],
                'error_type' => $this->determineMainErrorType($stats['error_types'] ?? []),
                'error_details' => [
                    'total_errors' => (int) ($stats['errors'] ?? 0),
                    'error_breakdown' => $stats['error_types'] ?? [],
                    'api_response_time' => (float) ($stats['api_response_time'] ?? 0),
                    'category_response_time' => (float) ($stats['category_response_time'] ?? 0),
                    'categories_processed' => (int) ($stats['categories_processed'] ?? 0),
                    'skipped_no_category_map' => (int) ($stats['skipped_no_category_map'] ?? 0),
                    'non_english_skipped' => (int) ($stats['non_english_skipped'] ?? 0),
                    'non_english_included' => (int) ($stats['non_english_included'] ?? 0),
                    'empty_title_skipped' => (int) ($stats['empty_title_skipped'] ?? 0),
                    'location_filtered' => (int) ($stats['location_filtered'] ?? 0)
                ]
            ];
        } catch (\Exception $e) {
            Log::error('AcbarJobService: Error formatting execution stats', [
                'error' => $e->getMessage(),
                'stats' => $stats
            ]);

            return [
                'jobs_fetched' => 0,
                'jobs_by_category' => [],
                'error_type' => 'UNKNOWN',
                'error_details' => ['total_errors' => 1, 'format_error' => $e->getMessage()]
            ];
        }
    }

    /**
     * Unified provider sync + aggregation entrypoint.
     * Accepts provider category identifiers and optional schedule rule id.
     */
    public function syncAndAggregate(?array $providerCategoryIdentifiers = null, ?int $scheduleRuleId = null): array
    {
        return $this->syncAcbarJobs($providerCategoryIdentifiers ?? [], $scheduleRuleId);
    }

    /**
     * Determine the main error type from error statistics
     * 
     * @param array $errorTypes
     * @return string
     */
    private function determineMainErrorType(array $errorTypes): string
    {
        if (empty($errorTypes)) {
            return 'NONE';
        }

        // Return the error type with the highest count
        arsort($errorTypes);
        return array_key_first($errorTypes);
    }

    /**
     * Process a single ACBAR category completely with full backend completion
     * This ensures all database operations, events, and background jobs are finished
     * before proceeding to the next category.
     *
     * @param string $providerIdentifier ACBAR provider identifier
     * @param int|null $scheduleRuleId Optional schedule rule ID for dynamic location filtering
     * @return array
     */
    private function processAcbarCategoryCompletely(string $providerIdentifier, ?int $scheduleRuleId = null): array
    {
        $startTime = microtime(true);
        $stats = [
            'created' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped_no_category_map' => 0,
            'category_processed' => false,
            'non_english_skipped' => 0,
            'non_english_included' => 0,
            'empty_title_skipped' => 0,
            'jobs_fetched' => 0,
            'jobs_by_category' => [],
            'error_types' => [],
            'category_response_time' => 0
        ];

        try {
            Log::info("ACBAR Category Processing: Starting complete processing", [
                'provider_identifier' => $providerIdentifier,
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);

            // Step 1: Get dynamic locations if schedule rule ID is provided
            $locationIds = [];
            if ($scheduleRuleId) {
                try {
                    $translatedFilters = $this->filterRepository->getAcbarTranslatedFilters($scheduleRuleId);
                    $locationIds = $translatedFilters['location_ids'] ?? [];

                    Log::debug("ACBAR Category Processing: Got dynamic locations from schedule rule", [
                        'schedule_rule_id' => $scheduleRuleId,
                        'location_ids' => $locationIds,
                        'location_count' => count($locationIds)
                    ]);
                } catch (\Exception $e) {
                    Log::warning("ACBAR Category Processing: Failed to get dynamic locations, using default", [
                        'schedule_rule_id' => $scheduleRuleId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // If no dynamic locations, use default location
            if (empty($locationIds)) {
                $locationIds = [(string)$this->locationId];
                Log::debug("ACBAR Category Processing: Using default location", [
                    'default_location_id' => $this->locationId
                ]);
            }

            // Step 2: Process the category for each location
            $originalStats = [
                'created' => 0,
                'updated' => 0,
                'errors' => 0,
                'skipped_no_category_map' => 0,
                'category_processed' => false
            ];

            foreach ($locationIds as $locationId) {
                Log::debug("ACBAR Category Processing: Processing category-location combination", [
                    'provider_identifier' => $providerIdentifier,
                    'location_id' => $locationId
                ]);

                $locationStats = $this->syncAcbarCategory($providerIdentifier, (string)$locationId);

                // Aggregate stats from this location
                $originalStats['created'] += $locationStats['created'];
                $originalStats['updated'] += $locationStats['updated'];
                $originalStats['errors'] += $locationStats['errors'];
                $originalStats['skipped_no_category_map'] += $locationStats['skipped_no_category_map'];
                $originalStats['category_processed'] = $originalStats['category_processed'] || $locationStats['category_processed'];
            }

            // Step 3: Ensure all database transactions are committed
            \DB::commit(); // Ensure any pending transactions are committed

            // Step 4: Wait for any background job processing to complete
            // (This is important if job processing triggers events or queue jobs)
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch(); // Handle any pending signals
            }

            // Step 4: Clear memory and optimize for next category
            $this->clearCategoryCache();
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles(); // Force garbage collection
            }

            // Merge basic stats
            $stats['created'] = $originalStats['created'];
            $stats['updated'] = $originalStats['updated'];
            $stats['errors'] = $originalStats['errors'];
            $stats['skipped_no_category_map'] = $originalStats['skipped_no_category_map'];
            $stats['category_processed'] = $originalStats['category_processed'];

            // Calculate health dashboard metrics
            $stats['jobs_fetched'] = $stats['created'] + $stats['updated'];

            // Get category name for tracking
            $categoryName = $this->getAcbarCategoryName($providerIdentifier);
            if ($categoryName && $stats['jobs_fetched'] > 0) {
                $stats['jobs_by_category'][$categoryName] = $stats['jobs_fetched'];
            }

            $stats['category_response_time'] = microtime(true) - $startTime;

            // After completing the full category processing across locations,
            // send aggregated notifications via the centralized hub
            try {
                $hub = app(\Modules\JobSeeker\Services\JobNotificationService::class);
                $newJobs = $this->aggregatedNewJobs ?? [];
                $updatedJobs = $this->aggregatedUpdatedJobs ?? [];

                // Use centralized MissedJobService to find missed jobs
                $missedJobsCollection = $this->missedJobService->findMissedJobs('acbar', 48);
                $missedJobs = [];

                Log::info("AcbarJobService: Found " . $missedJobsCollection->count() . " potential missed jobs from MissedJobService");

                if ($missedJobsCollection->isNotEmpty()) {
                    foreach ($missedJobsCollection as $job) {
                        // Include based on rule: English-only unless allowNonEnglish is enabled
                        $detector = app(\Modules\JobSeeker\Services\LanguageDetectionService::class);
                        $passesLanguage = $this->allowNonEnglish ? true : $detector->isEnglishTitle((string) $job->position);
                        if ($passesLanguage) {
                            // Check if job is already in newJobs (to avoid duplicate notifications)
                            $isDuplicate = false;
                            foreach ($newJobs as $newJob) {
                                if (isset($newJob['id']) && $newJob['id'] == $job->id) {
                                    $isDuplicate = true;
                                    break;
                                }
                            }

                            if (!$isDuplicate) {
                                $missedJobs[] = [
                                    'id' => $job->id,
                                    'position' => $job->position,
                                    'company_name' => $job->company_name,
                                    'locations' => $job->locations,
                                    'publish_date' => $job->publish_date,
                                    'categories' => $job->categories ? $job->categories->pluck('id')->toArray() : []
                                ];
                                Log::info("AcbarJobService: Adding recent job to missed notifications", [
                                    'id' => $job->id,
                                    'position' => $job->position
                                ]);
                            } else {
                                Log::info("AcbarJobService: Skipping duplicate job in missed notifications", [
                                    'id' => $job->id,
                                    'position' => $job->position
                                ]);
                            }
                        }
                    }
                    Log::info("AcbarJobService: Added " . count($missedJobs) . " jobs to missed notifications queue");
                }

                // Build schedule context for missed call tracking
                $scheduleContext = [
                    'command' => 'jobseeker:sync-acbar-jobs',
                    'provider' => 'acbar',
                    'schedule_rule_id' => $scheduleRuleId ?? null,
                    'execution_id' => $this->executionId,
                    'trace_id' => $this->traceId,
                ];

                // Use unified notification service for canonical category-based notifications
                $sent = $hub->notifyAggregatedJobs($newJobs, $updatedJobs, $missedJobs, $scheduleContext);

                Log::info('ACBAR Category Processing: Notifications processed via unified JobNotificationService', [
                    'provider_identifier' => $providerIdentifier,
                    'new_jobs' => count($newJobs),
                    'updated_jobs' => count($updatedJobs),
                    'result' => $sent,
                    'schedule_context' => $scheduleContext,
                    'trace_id' => $this->traceId,
                    'execution_id' => $this->executionId,
                ]);
            } catch (\Throwable $e) {
                Log::error('ACBAR Category Processing: Error sending notifications via unified service', [
                    'provider_identifier' => $providerIdentifier,
                    'error' => $e->getMessage(),
                    'trace_id' => $this->traceId,
                    'execution_id' => $this->executionId,
                ]);
            } finally {
                // Reset aggregation arrays for next category
                $this->aggregatedNewJobs = [];
                $this->aggregatedUpdatedJobs = [];
            }

            Log::info("ACBAR Category Processing: Complete processing finished", [
                'provider_identifier' => $providerIdentifier,
                'category_name' => $categoryName,
                'jobs_fetched' => $stats['jobs_fetched'],
                'jobs_created' => $stats['created'],
                'jobs_updated' => $stats['updated'],
                'response_time' => round($stats['category_response_time'], 2),
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);

        } catch (\Exception $e) {
            // Classify and track the error
            $errorType = $this->classifyError($e);
            $stats['error_types'][$errorType] = 1;
            $stats['errors']++;
            $stats['category_response_time'] = microtime(true) - $startTime;

            Log::error("ACBAR Category Processing: Error in complete processing", [
                'provider_identifier' => $providerIdentifier,
                'error_type' => $errorType,
                'error' => $e->getMessage(),
                'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2)
            ]);
        }

        return $stats;
    }

    /**
     * Implement smart delay between categories based on processing results
     * This prevents rate limiting and ensures proper system recovery time
     * 
     * @param array $stats Processing statistics from the category
     * @param float $categoryDuration Time taken to process the category
     * @return void
     */
    private function smartDelayBetweenCategories(array $stats, float $categoryDuration): void
    {
        // Base delay (minimum wait time)
        $baseDelay = 3; // 3 seconds minimum

        // Calculate additional delay based on jobs processed
        $jobsProcessed = $stats['jobs_fetched'] ?? 0;
        $jobBasedDelay = min($jobsProcessed * 0.1, 5); // Max 5 seconds for jobs

        // Calculate additional delay based on processing time
        $timeBasedDelay = min($categoryDuration * 0.2, 3); // Max 3 seconds for time

        // Additional delay if there were errors
        $errorDelay = 0;
        if (!empty($stats['error_types'])) {
            $errorDelay = 2; // Extra 2 seconds if there were errors
        }

        // Calculate total delay
        $totalDelay = $baseDelay + $jobBasedDelay + $timeBasedDelay + $errorDelay;
        $totalDelay = min($totalDelay, 15); // Cap at 15 seconds maximum

        Log::info("ACBAR Sequential Processing: Smart delay between categories", [
            'base_delay' => $baseDelay,
            'job_based_delay' => round($jobBasedDelay, 2),
            'time_based_delay' => round($timeBasedDelay, 2),
            'error_delay' => $errorDelay,
            'total_delay' => round($totalDelay, 2),
            'jobs_processed' => $jobsProcessed,
            'category_duration' => round($categoryDuration, 2),
            'has_errors' => !empty($stats['error_types'])
        ]);

        // Execute the delay
        sleep((int) $totalDelay);

        // Add micro-delay for fractional seconds
        $microSeconds = ($totalDelay - floor($totalDelay)) * 1000000;
        if ($microSeconds > 0) {
            usleep((int) $microSeconds);
        }
    }

    /**
     * Clear category-specific cache and optimize memory usage
     * 
     * @return void
     */
    private function clearCategoryCache(): void
    {
        // Clear the category cache
        $this->categoryCache = [];

        // Clear any static caches
        static::$canonicalCategoriesCache = [];

        Log::debug("ACBAR Category Processing: Cache cleared for next category");
    }

    /**
     * Send synchronous email notifications for a processed job
     * This method is adapted from JobsAfService::sendJobNotificationEmail()
     * to provide immediate email delivery for ACBAR jobs
     *
     * @param \Modules\JobSeeker\Entities\Job $job The processed job
     * @return bool Success status
     */
    protected function sendSynchronousEmailNotification(\Modules\JobSeeker\Entities\Job $job): bool
    {
        // Deprecated in favor of JobNotificationHub aggregation. Retained for BC; no-op.
        Log::debug('AcbarJobService: Deprecated sendSynchronousEmailNotification() called; ignoring.', [
            'job_id' => $job->id ?? null,
        ]);
        return false;
    }

    /**
     * Format jobs for email template
     * Adapted from JobsAfService to ensure consistent email formatting
     *
     * @param array $jobs
     * @return array
     */
    protected function formatJobsForEmail(array $jobs): array
    {
        return array_map(function ($job) {
            return [
                'id' => $job['id'] ?? null,
                'position' => $job['position'] ?? 'Unknown Position',
                'company_name' => $job['company_name'] ?? 'Unknown Company',
                'locations' => $job['locations'] ?? 'Not specified',
                'expire_date' => $job['expire_date'] ?? null,
                'publish_date' => $job['publish_date'] ?? null,
                'source' => $job['source'] ?? 'ACBAR',
                // Add any other fields needed by the email template
            ];
        }, $jobs);
    }

    /**
     * Fetch jobs from the provider and send notifications.
     * 
     * This method should:
     * 1. Fetch jobs from the external provider API
     * 2. Process and categorize jobs with provider_category_ids
     * 3. Call JobNotificationService for unified notification handling
     * 
     * @param array $scheduleContext Context including trace_id, execution_id
     * @return array Statistics about the sync process
     */
    public function fetchAndNotifyJobs(array $scheduleContext): array
    {
        // Extract parameters from schedule context for backward compatibility
        $providerIdentifiers = $scheduleContext['provider_identifiers'] ?? null;
        $scheduleRuleId = $scheduleContext['schedule_rule_id'] ?? null;
        
        // Call existing implementation with new unified interface
        return $this->syncAcbarJobs($providerIdentifiers, $scheduleRuleId);
    }
}
